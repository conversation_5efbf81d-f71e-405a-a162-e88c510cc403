# Figure 2(b) Baseline Experiment Reproduction Guide

## 概述 (Overview)

本指南提供了复现论文图2(b)基线实验的完整方案。图2(b)展示了标准VGG16模型（标称网络）在不同硬件噪声条件下的性能退化情况。

**现已支持CIFAR-10和CIFAR-100两个数据集！**

## 实验目标 (Experiment Objective)

评估一个**标准训练的VGG16模型**在两种硬件噪声下的图像分类准确率：
1. **RRAM噪声** (对数正态乘性噪声)
2. **PCM噪声** (感知正态加性噪声)

支持的数据集：
- **CIFAR-10** (10类，已有预训练模型)
- **CIFAR-100** (100类，需要训练或正在训练中)

## 关键文件定位 (Key Files Located)

### 1. 脚本文件 (Scripts)
- **主要测试脚本**: `baseline_figure2b_test.py` - 完整的基线实验脚本
- **快速测试脚本**: `quick_test_baseline.py` - 单次测试验证脚本
- **批量运行脚本**: `run_figure2b_simple.sh` - 自动化批量测试

### 2. 模型文件 (Model Files)

#### CIFAR-10模型 (已可用)
- **标称网络路径**: `checkpoint_NC/rram/cifar10/vgg16_nonid/ckpt_cifar10_nonid_0.0.pth`
- **模型大小**: ~57MB
- **模型类型**: 标准训练的VGG16（无噪声训练，10类）

#### CIFAR-100模型 (需要训练)
- **标称网络路径**: `checkpoint_NC/cifar100/vgg16/ckpt_cifar100_nonid_0.0.pth`
- **模型类型**: 标准训练的VGG16（无噪声训练，100类）
- **训练状态**: 正在训练中或需要训练

### 3. 噪声实现 (Noise Implementation)

#### RRAM噪声 (Log-normal Multiplicative)
```python
def add_rram_noise(model, sigma=0.1):
    # 公式: W_new = W_nominal * exp(θ), θ ~ N(0, σ²)
    noise = torch.normal(0, sigma, size=weight_copy.size())
    module.weight.data = torch.exp(noise) * weight_copy
```

#### PCM噪声 (Perceptual Normal Additive)
```python
def add_pcm_noise(model, eta=0.1):
    # 公式: W_new = W_nominal + N(0, σ²), σ = η * W_max
    max_value = torch.max(torch.abs(weight_copy))
    std = eta * max_value
    noise = torch.normal(0, std, size=weight_copy.size())
    module.weight.data = weight_copy + noise
```

## 运行命令 (Execution Commands)

### 步骤0: CIFAR-100模型训练 (如需要)
```bash
# 训练CIFAR-100的标准VGG16模型（单卡训练）
python main_vgg.py --dataset cifar100 --model_name vgg16 --noise 0.0 --epoch 200 --lr 0.01 --batch_size 128

# 训练时间：约2-4小时（取决于GPU性能）
# 训练完成后模型将保存到：checkpoint_NC/cifar100/vgg16/ckpt_cifar100_nonid_0.0.pth
```

### 方法1: 快速单次测试
```bash
# CIFAR-10测试
python quick_test_baseline.py --dataset cifar10 --noise_type rram --noise_level 0.0
python quick_test_baseline.py --dataset cifar10 --noise_type rram --noise_level 0.2
python quick_test_baseline.py --dataset cifar10 --noise_type pcm --noise_level 0.04

# CIFAR-100测试（需要先完成训练）
python quick_test_baseline.py --dataset cifar100 --noise_type rram --noise_level 0.0
python quick_test_baseline.py --dataset cifar100 --noise_type rram --noise_level 0.2
python quick_test_baseline.py --dataset cifar100 --noise_type pcm --noise_level 0.04
```

### 方法2: 批量自动化测试
```bash
# CIFAR-10完整实验
./run_figure2b_simple.sh cifar10

# CIFAR-100完整实验（需要先完成训练）
./run_figure2b_simple.sh cifar100
```

### 方法3: 完整统计测试（多次试验）
```bash
# RRAM噪声测试（包含统计分析）
python baseline_figure2b_test.py --noise_type rram --num_trials 5

# PCM噪声测试（包含统计分析）
python baseline_figure2b_test.py --noise_type pcm --num_trials 5
```

## 噪声强度范围 (Noise Level Ranges)

### RRAM噪声强度 (σ值)
- 测试范围: [0.0, 0.1, 0.2, 0.3, 0.4, 0.5]
- 参数含义: 对数正态分布的标准差

### PCM噪声强度 (η值)
- 测试范围: [0.0, 0.02, 0.04, 0.06, 0.08, 0.1]
- 参数含义: 相对于权重最大值的噪声比例

## 预期结果 (Expected Results)

基于测试，预期结果如下：

### CIFAR-10结果
#### 无噪声基线
- **准确率**: ~92.97%

#### RRAM噪声 (σ=0.2)
- **准确率**: ~91.59%
- **性能下降**: ~1.38%

#### PCM噪声 (η=0.04)
- **准确率**: ~92.10%
- **性能下降**: ~0.87%

### CIFAR-100结果 (基于当前训练模型)
#### 无噪声基线
- **准确率**: ~53.51% (训练中，会继续提升)

#### RRAM噪声 (σ=0.2)
- **准确率**: ~50.29%
- **性能下降**: ~3.22%

#### PCM噪声 (η=0.04)
- **准确率**: ~52.01%
- **性能下降**: ~1.50%

## 实验验证 (Verification)

所有脚本已通过测试验证：
- ✅ 模型加载正常
- ✅ 噪声注入功能正确
- ✅ CIFAR-10数据集加载成功
- ✅ GPU/CPU自动检测工作
- ✅ 结果输出格式正确

## 故障排除 (Troubleshooting)

### 常见问题
1. **模型文件不存在**: 确认路径 `checkpoint_NC/rram/cifar10/vgg16_nonid/ckpt_cifar10_nonid_0.0.pth` 存在
2. **依赖缺失**: 脚本已优化，只导入必要的VGG16模型，避免einops等额外依赖
3. **内存不足**: 可以调整batch_size参数

### 环境要求
- PyTorch
- torchvision
- CUDA (可选，自动检测)

## 使用建议 (Usage Recommendations)

1. **首次运行**: 使用 `quick_test_baseline.py` 验证环境
2. **完整实验**: 使用 `run_figure2b_simple.sh` 获得所有数据点
3. **统计分析**: 使用 `baseline_figure2b_test.py` 进行多次试验统计

## 训练状态监控 (Training Status Monitoring)

### 检查CIFAR-100训练进度
```bash
# 检查训练是否在进行
ps aux | grep main_vgg.py

# 查看训练日志（如果使用nohup）
tail -f nohup.out

# 检查模型文件是否存在
ls -la checkpoint_NC/cifar100/vgg16/ckpt_cifar100_nonid_0.0.pth

# 测试当前训练的模型
python quick_test_baseline.py --dataset cifar100 --model_path checkpoint_NC/cifar100/vgg16/ckpt_cifar100_nonid_0.0.pth --noise_type rram --noise_level 0.0
```

### 训练时间估计
- **CIFAR-100训练**: 约2-4小时（取决于GPU性能）
- **当前状态**: 正在进行第17个epoch，准确率约67%
- **预期最终准确率**: 70-75%

## 注意事项 (Important Notes)

1. **单卡训练**: 所有训练都使用单卡，已禁用DataParallel
2. **模型兼容性**: 脚本已修改以支持CIFAR-10和CIFAR-100的不同类别数
3. **噪声实现**: 已修复tensor类型问题，确保噪声注入正确
4. **路径管理**: 自动创建必要的目录结构

这些脚本和命令将帮助您完整复现论文图2(b)中的基线实验结果。
