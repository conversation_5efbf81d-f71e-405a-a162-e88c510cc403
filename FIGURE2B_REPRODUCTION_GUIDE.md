# Figure 2(b) Baseline Experiment Reproduction Guide

## 概述 (Overview)

本指南提供了复现论文图2(b)基线实验的完整方案。图2(b)展示了标准VGG16模型（标称网络）在不同硬件噪声条件下的性能退化情况。

**现已支持CIFAR-10和CIFAR-100两个数据集！**

## 实验目标 (Experiment Objective)

评估一个**标准训练的VGG16模型**在两种硬件噪声下的图像分类准确率：
1. **RRAM噪声** (对数正态乘性噪声)
2. **PCM噪声** (感知正态加性噪声)

支持的数据集：
- **CIFAR-10** (10类)
- **CIFAR-100** (100类)

## 关键文件定位 (Key Files Located)

### 1. 脚本文件 (Scripts)
- **主要测试脚本**: `baseline_figure2b_test.py` - 完整的基线实验脚本
- **快速测试脚本**: `quick_test_baseline.py` - 单次测试验证脚本
- **批量运行脚本**: `run_figure2b_simple.sh` - 自动化批量测试

### 2. 模型文件 (Model Files)
#### CIFAR-10模型: `checkpoint_NC/rram/cifar10/vgg16_nonid/ckpt_cifar10_nonid_0.0.pth`
#### CIFAR-100模型: `checkpoint_NC/cifar100/vgg16/ckpt_cifar100_nonid_0.0.pth`

### 3. 噪声实现 (Noise Implementation)

#### RRAM噪声 (Log-normal Multiplicative)
```python
def add_rram_noise(model, sigma=0.1):
    # 公式: W_new = W_nominal * exp(θ), θ ~ N(0, σ²)
    noise = torch.normal(0, sigma, size=weight_copy.size())
    module.weight.data = torch.exp(noise) * weight_copy
```

#### PCM噪声 (Perceptual Normal Additive)
```python
def add_pcm_noise(model, eta=0.1):
    # 公式: W_new = W_nominal + N(0, σ²), σ = η * W_max
    # 注意：使用torch.max(weight_copy)而不是torch.max(torch.abs(weight_copy))
    max_value = torch.max(weight_copy)
    std = eta * max_value
    noise = torch.normal(0, std, size=weight_copy.size())
    module.weight.data = noise + weight_copy
```

## 运行命令 (Execution Commands)

### 步骤0: 模型训练 (如需要)
```bash
# 训练CIFAR-10的标准VGG16模型（单卡训练）
python main_vgg.py --dataset cifar10 --model_name vgg16 --noise 0.0 --epoch 200 --lr 0.01 --batch_size 128
# 训练完成后模型将保存到：checkpoint_NC/cifar10/vgg16/ckpt_cifar10_nonid_0.0.pth

# 训练CIFAR-100的标准VGG16模型（单卡训练）
python main_vgg.py --dataset cifar100 --model_name vgg16 --noise 0.0 --epoch 200 --lr 0.01 --batch_size 128
# 训练完成后模型将保存到：checkpoint_NC/cifar100/vgg16/ckpt_cifar100_nonid_0.0.pth
```

### 方法1: 快速单次测试
```bash
# CIFAR-10测试
python quick_test_baseline.py --dataset cifar10 --noise_type rram --noise_level 0.2
python quick_test_baseline.py --dataset cifar10 --noise_type pcm --noise_level 0.12

# CIFAR-100测试
python quick_test_baseline.py --dataset cifar100 --noise_type rram --noise_level 0.2
python quick_test_baseline.py --dataset cifar100 --noise_type pcm --noise_level 0.12
```

### 方法2: 批量自动化测试
```bash
# CIFAR-10完整实验
./run_figure2b_simple.sh cifar10

# CIFAR-100完整实验
./run_figure2b_simple.sh cifar100
```

### 方法3: 完整统计测试（多次试验）
```bash
# CIFAR-10 RRAM噪声测试（包含统计分析）
python baseline_figure2b_test.py --dataset cifar10 --noise_type rram --num_trials 10

# CIFAR-10 PCM噪声测试（包含统计分析）
python baseline_figure2b_test.py --dataset cifar10 --noise_type pcm --num_trials 10

# CIFAR-100 RRAM噪声测试（包含统计分析）
python baseline_figure2b_test.py --dataset cifar100 --noise_type rram --num_trials 10

# CIFAR-100 PCM噪声测试（包含统计分析）
python baseline_figure2b_test.py --dataset cifar100 --noise_type pcm --num_trials 10
```

## 噪声强度范围 (Noise Level Ranges)

### RRAM噪声强度 (σ值)
- 测试范围: [0.0, 0.1, 0.2, 0.3, 0.4, 0.5]
- 参数含义: 对数正态分布的标准差

### PCM噪声强度 (η值)
- 测试范围: [0.0, 0.02, 0.04, 0.06, 0.08, 0.1]
- 参数含义: 相对于权重最大值的噪声比例

### 完整结果对比表 (Complete Results Comparison)

| 噪声类型 | 噪声强度 | Cifar10 准确率 | Cifar10 标准差 | Cifar100 准确率 | Cifar100 标准差 |
|----------|---------|--------------|--|-----|----------|
| 无噪声   | 0.0      | 92.97 |  -  | 71.74  |  -  | 
| RRAM     | 0.1    | 92.66 |  0.11  | 70.82 |  0.29  |
| RRAM     | 0.2    | 91.69 |  0.26  | 68.46 |  0.37  |
| RRAM     | 0.3    | 89.00 |  0.67  | 60.35 |  1.96  |
| RRAM     | 0.4    | 82.50 |  2.09  | 47.65 |  3.12  |
| RRAM     | 0.5    | 65.11 |  5.43  | 22.06 |  6.18  |
| PCM      | 0.02   | 92.12  |  0.00  | 70.88 |  0.21  |
| PCM      | 0.04   | 91.91  |  0.00  | 67.80 |  0.72  |
| PCM      | 0.06   | 90.66  |  0.42  | 61.84 |  2.42  |
| PCM      | 0.08   | 88.18  |  0.77  | 50.22 |  3.90  |
| PCM      | 0.10   | 82.06  |  2.73  | 33.63 |  5.35  |
| PCM      | 0.12   | 76.89  |  5.55  | 17.70 |  3.77  |

**观察结果**:
- CIFAR-100对噪声更敏感，性能下降更明显
- 高强度噪声(σ≥0.4, η≥0.08)对两个数据集都有显著影响
- PCM和RRAM噪声在相似强度下表现出类似的性能下降模式

## 实验验证 (Verification)

所有脚本已通过测试验证：
- ✅ 模型加载正常
- ✅ 噪声注入功能正确
- ✅ CIFAR-10和CIFAR-100数据集加载成功
- ✅ GPU/CPU自动检测工作
- ✅ 结果输出格式正确
- ✅ **噪声实现与原有代码完全一致**

### 噪声实现一致性验证
通过详细的代码比较和数值测试，确认：

1. **RRAM噪声实现**：与原有`add_noise_to_conv_layers()`函数完全一致
   - 使用相同的公式：`W_new = W_nominal * exp(θ)`
   - 使用相同的参数：`θ ~ N(0, σ²)`

2. **PCM噪声实现**：与原有`add_noise_to_conv_layers_pcm()`函数完全一致
   - 使用相同的公式：`W_new = W_nominal + N(0, σ²)`
   - 使用相同的最大值计算：`torch.max(weight_copy)`（不使用绝对值）
   - 使用相同的加法顺序：`noise + weight_copy`

3. **数值验证**：使用相同随机种子的测试确认两种实现产生完全相同的结果

## 故障排除 (Troubleshooting)

### 常见问题
1. **模型文件不存在**: 确认路径 `checkpoint_NC/rram/cifar10/vgg16_nonid/ckpt_cifar10_nonid_0.0.pth` 存在
2. **依赖缺失**: 脚本已优化，只导入必要的VGG16模型，避免einops等额外依赖
3. **内存不足**: 可以调整batch_size参数

### 环境要求
- PyTorch
- torchvision
- CUDA (可选，自动检测)

## 使用建议 (Usage Recommendations)

1. **首次运行**: 使用 `quick_test_baseline.py` 验证环境
2. **完整实验**: 使用 `run_figure2b_simple.sh` 获得所有数据点
3. **统计分析**: 使用 `baseline_figure2b_test.py` 进行多次试验统计

## 注意事项 (Important Notes)

1. **单卡训练**: 所有训练都使用单卡，已禁用DataParallel
2. **模型兼容性**: 脚本已修改以支持CIFAR-10和CIFAR-100的不同类别数
3. **噪声实现**: 已修复tensor类型问题，确保噪声注入正确
4. **路径管理**: 自动创建必要的目录结构

这些脚本和命令将帮助您完整复现论文图2(b)中的基线实验结果。
