#!/usr/bin/env python3
"""
Baseline Test Script for Figure 2(b) Reproduction
==================================================

This script reproduces the baseline experiment shown in Figure 2(b) of the paper.
It evaluates a standard VGG16 model (nominal network) under different noise conditions:

1. Log-normal multiplicative noise (RRAM): W_new = W_nominal * exp(θ), θ ~ N(0, σ²)
2. Perceptual normal additive noise (PCM): W_new = W_nominal + N(0, σ²), σ = η * W_max

Usage:
    python baseline_figure2b_test.py --noise_type rram --model_path checkpoint_NC/rram/cifar10/vgg16_nonid/ckpt_cifar10_nonid_0.0.pth
    python baseline_figure2b_test.py --noise_type pcm --model_path checkpoint_NC/rram/cifar10/vgg16_nonid/ckpt_cifar10_nonid_0.0.pth
"""

import torch
import torch.nn as nn
import torch.backends.cudnn as cudnn
import torchvision
import torchvision.transforms as transforms
from torchvision import datasets
import os
import argparse
import statistics
import copy

from models.vgg16 import vgg16
from utils import progress_bar

def parse_args():
    parser = argparse.ArgumentParser(description='Baseline Test for Figure 2(b) - VGG16 Noise Robustness')
    parser.add_argument('--model_path',
                        default='',
                        help='Path to the nominal (standard trained) VGG16 model (auto-detected if empty)', type=str)
    parser.add_argument('--noise_type',
                        choices=['rram', 'pcm'],
                        default='rram',
                        help='Type of noise to inject: rram (log-normal multiplicative) or pcm (perceptual additive)')
    parser.add_argument('--model_name', default='vgg16', help='Model architecture', type=str)
    parser.add_argument('--batch_size', default=64, help='Batch size for testing', type=int)
    parser.add_argument('--num_trials', default=5, help='Number of trials per noise level', type=int)
    parser.add_argument('--device', default='auto', help='Device to use: auto, cpu, or cuda', type=str)
    parser.add_argument('--dataset',
                        choices=['cifar10', 'cifar100'],
                        default='cifar10',
                        help='Dataset to use: cifar10 or cifar100')
    return parser.parse_args()

def setup_device(device_arg):
    """Setup computation device"""
    if device_arg == 'auto':
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
    else:
        device = device_arg
    print(f'Using device: {device}')
    return device

def load_data(dataset_name, batch_size):
    """Load test dataset (CIFAR-10 or CIFAR-100)"""
    kwargs = {'num_workers': 1, 'pin_memory': True}

    if dataset_name == 'cifar10':
        print('==> Preparing CIFAR-10 data..')
        test_loader = torch.utils.data.DataLoader(
            datasets.CIFAR10('./data.cifar10', train=False, download=True,
                           transform=transforms.Compose([
                               transforms.ToTensor(),
                               transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2023, 0.1994, 0.2010))
                           ])),
            batch_size=batch_size, shuffle=False, **kwargs)
        num_classes = 10
    else:  # cifar100
        print('==> Preparing CIFAR-100 data..')
        test_loader = torch.utils.data.DataLoader(
            datasets.CIFAR100('./data.cifar100', train=False, download=True,
                           transform=transforms.Compose([
                               transforms.ToTensor(),
                               transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2023, 0.1994, 0.2010))
                           ])),
            batch_size=batch_size, shuffle=False, **kwargs)
        num_classes = 100

    print(f'Test dataset size: {len(test_loader.dataset)}')
    return test_loader, num_classes

def create_model(model_name, num_classes=10):
    """Create the specified model"""
    print(f'==> Building {model_name} model with {num_classes} classes..')
    if model_name == "vgg16":
        net = vgg16(num_classes=num_classes)
    else:
        raise ValueError(f"Unsupported model: {model_name}")
    return net

def add_rram_noise(model, sigma=0.1):
    """
    Add log-normal multiplicative noise (RRAM device noise)
    Formula: W_new = W_nominal * exp(θ), where θ ~ N(0, σ²)
    """
    for module in model.children():
        if isinstance(module, nn.Conv2d):
            with torch.no_grad():
                weight_copy = module.weight.data.clone()
                noise = torch.normal(0, sigma, size=weight_copy.size())
                if weight_copy.is_cuda:
                    noise = noise.cuda()
                module.weight.data = torch.exp(noise) * weight_copy
        elif isinstance(module, nn.Linear):
            with torch.no_grad():
                weight_copy = module.weight.data.clone()
                noise = torch.normal(0, sigma, size=weight_copy.size())
                if weight_copy.is_cuda:
                    noise = noise.cuda()
                module.weight.data = torch.exp(noise) * weight_copy
        elif isinstance(module, nn.Module):
            add_rram_noise(module, sigma=sigma)

def add_pcm_noise(model, eta=0.1):
    """
    Add perceptual normal additive noise (PCM device noise)
    Formula: W_new = W_nominal + N(0, σ²), where σ = η * W_max
    """
    for module in model.children():
        if isinstance(module, nn.Conv2d):
            with torch.no_grad():
                weight_copy = module.weight.data.clone()
                max_value = torch.max(torch.abs(weight_copy)).item()
                std = eta * max_value
                noise = torch.normal(0, std, size=weight_copy.size())
                if weight_copy.is_cuda:
                    noise = noise.cuda()
                module.weight.data = weight_copy + noise
        elif isinstance(module, nn.Linear):
            with torch.no_grad():
                weight_copy = module.weight.data.clone()
                max_value = torch.max(torch.abs(weight_copy)).item()
                std = eta * max_value
                noise = torch.normal(0, std, size=weight_copy.size())
                if weight_copy.is_cuda:
                    noise = noise.cuda()
                module.weight.data = weight_copy + noise
        elif isinstance(module, nn.Module):
            add_pcm_noise(module, eta=eta)

def evaluate_model(model, test_loader, device):
    """Evaluate model accuracy on test set"""
    model.eval()
    criterion = nn.CrossEntropyLoss()
    test_loss = 0
    correct = 0
    total = 0
    
    with torch.no_grad():
        for batch_idx, (inputs, targets) in enumerate(test_loader):
            inputs, targets = inputs.to(device), targets.to(device)
            outputs = model(inputs)
            loss = criterion(outputs, targets)
            
            test_loss += loss.item()
            _, predicted = outputs.max(1)
            total += targets.size(0)
            correct += predicted.eq(targets).sum().item()
            
            if batch_idx % 50 == 0:  # Print progress every 50 batches
                progress_bar(batch_idx, len(test_loader), 'Loss: %.3f | Acc: %.3f%% (%d/%d)'
                           % (test_loss / (batch_idx + 1), 100. * correct / total, correct, total))
    
    accuracy = 100. * correct / total
    return accuracy

def run_baseline_experiment(args):
    """Run the complete baseline experiment"""
    device = setup_device(args.device)
    test_loader, num_classes = load_data(args.dataset, args.batch_size)

    # Determine model path if not provided
    if not args.model_path:
        if args.dataset == 'cifar10':
            model_path = 'checkpoint_NC/rram/cifar10/vgg16_nonid/ckpt_cifar10_nonid_0.0.pth'
        else:  # cifar100
            model_path = 'checkpoint_NC/cifar100/vgg16/ckpt_cifar100_nonid_0.0.pth'
    else:
        model_path = args.model_path

    # Load the nominal (standard trained) model
    print(f'==> Loading nominal model from {model_path}')
    if not os.path.exists(model_path):
        error_msg = f"Model file not found: {model_path}"
        if args.dataset == 'cifar100':
            error_msg += "\nYou need to train the CIFAR-100 model first using:"
            error_msg += "\npython main_vgg.py --dataset cifar100 --model_name vgg16 --noise 0.0 --epoch 200 --lr 0.01 --batch_size 128"
        raise FileNotFoundError(error_msg)

    # Define noise levels for each type
    if args.noise_type == 'rram':
        noise_levels = [0.0, 0.1, 0.2, 0.3, 0.4, 0.5]
        noise_param_name = 'σ'
        print("Testing RRAM noise (log-normal multiplicative)")
    else:  # pcm
        noise_levels = [0.02, 0.04, 0.06, 0.08, 0.1, 0.12]
        noise_param_name = 'η'
        print("Testing PCM noise (perceptual normal additive)")
    
    results = {}
    
    for noise_level in noise_levels:
        print(f'\n{"="*60}')
        print(f'Testing {args.noise_type.upper()} noise level {noise_param_name}={noise_level}')
        print(f'{"="*60}')
        
        trial_accuracies = []
        
        for trial in range(args.num_trials):
            print(f'\nTrial {trial + 1}/{args.num_trials}')

            # Create fresh model and load weights
            model = create_model(args.model_name, num_classes=num_classes)
            model = model.to(device)

            checkpoint = torch.load(model_path, map_location=device)
            model.load_state_dict(checkpoint)
            
            # Add noise if noise_level > 0
            if noise_level > 0:
                if args.noise_type == 'rram':
                    add_rram_noise(model, sigma=noise_level)
                else:  # pcm
                    add_pcm_noise(model, eta=noise_level)
            
            # Evaluate model
            accuracy = evaluate_model(model, test_loader, device)
            trial_accuracies.append(accuracy)
            print(f'Trial {trial + 1} accuracy: {accuracy:.2f}%')
        
        # Calculate statistics
        mean_acc = statistics.mean(trial_accuracies)
        std_acc = statistics.stdev(trial_accuracies) if len(trial_accuracies) > 1 else 0.0
        
        results[noise_level] = {
            'mean': mean_acc,
            'std': std_acc,
            'trials': trial_accuracies
        }
        
        print(f'\n{args.noise_type.upper()} {noise_param_name}={noise_level}: {mean_acc:.2f}% ± {std_acc:.2f}%')
    
    # Print final summary
    print(f'\n{"="*80}')
    print(f'FINAL RESULTS - {args.noise_type.upper()} Noise Baseline Test')
    print(f'Dataset: {args.dataset.upper()}')
    print(f'Model: {args.model_name} (nominal network, {num_classes} classes)')
    print(f'{"="*80}')

    for noise_level in noise_levels:
        result = results[noise_level]
        print(f'{noise_param_name}={noise_level:4.2f}: {result["mean"]:6.2f}% ± {result["std"]:5.2f}%')

    return results

if __name__ == '__main__':
    args = parse_args()
    results = run_baseline_experiment(args)
