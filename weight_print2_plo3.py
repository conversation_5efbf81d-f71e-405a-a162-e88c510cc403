import torch
import torch.nn as nn
import torch.quantization as quant

# 示例模型
class SimpleModel(nn.Module):
    def __init__(self):
        super(SimpleModel, self).__init__()
        self.fc = nn.Linear(10, 5)

    def forward(self, x):
        return self.fc(x)

# 创建模型并转换为量化模型
model = SimpleModel()

# 对模型进行量化
model.qconfig = quant.get_default_qconfig('fbgemm')
quant.prepare(model, inplace=True)  # 准备量化
quant.convert(model, inplace=True)  # 转换为量化模型

# 打印量化后的模型参数
for name, param in model.named_parameters():
    print(f"Parameter: {name}, Value: {param}")
