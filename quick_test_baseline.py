#!/usr/bin/env python3
"""
Quick Test Script for Figure 2(b) Baseline
===========================================

This is a simplified version for quick testing of the baseline experiment.
It tests a single noise level to verify the setup is working correctly.

Usage:
    python quick_test_baseline.py --noise_type rram --noise_level 0.1
    python quick_test_baseline.py --noise_type pcm --noise_level 0.02
"""

import torch
import torch.nn as nn
import torchvision.transforms as transforms
from torchvision import datasets
import os
import argparse

from models.vgg16 import vgg16
from utils import progress_bar

def parse_args():
    parser = argparse.ArgumentParser(description='Quick Baseline Test for Figure 2(b)')
    parser.add_argument('--model_path',
                        default='checkpoint_NC/rram/cifar10/vgg16_nonid/ckpt_cifar10_nonid_0.0.pth',
                        help='Path to the nominal VGG16 model', type=str)
    parser.add_argument('--noise_type',
                        choices=['rram', 'pcm'],
                        default='rram',
                        help='Type of noise: rram or pcm')
    parser.add_argument('--noise_level',
                        default=0.1, type=float,
                        help='Noise level (sigma for rram, eta for pcm)')
    parser.add_argument('--batch_size', default=64, type=int)
    parser.add_argument('--dataset',
                        choices=['cifar10', 'cifar100'],
                        default='cifar10',
                        help='Dataset to use: cifar10 or cifar100')
    return parser.parse_args()

def add_rram_noise(model, sigma=0.1):
    """Add RRAM noise: W_new = W_nominal * exp(θ), θ ~ N(0, σ²)"""
    for module in model.children():
        if isinstance(module, nn.Conv2d):
            with torch.no_grad():
                weight_copy = module.weight.data.clone()
                noise = torch.normal(0, sigma, size=weight_copy.size())
                if weight_copy.is_cuda:
                    noise = noise.cuda()
                module.weight.data = torch.exp(noise) * weight_copy
        elif isinstance(module, nn.Linear):
            with torch.no_grad():
                weight_copy = module.weight.data.clone()
                noise = torch.normal(0, sigma, size=weight_copy.size())
                if weight_copy.is_cuda:
                    noise = noise.cuda()
                module.weight.data = torch.exp(noise) * weight_copy
        elif isinstance(module, nn.Module):
            add_rram_noise(module, sigma=sigma)

def add_pcm_noise(model, eta=0.1):
    """Add PCM noise: W_new = W_nominal + N(0, σ²), σ = η * W_max"""
    for module in model.children():
        if isinstance(module, nn.Conv2d):
            with torch.no_grad():
                weight_copy = module.weight.data.clone()
                max_value = torch.max(weight_copy)
                std = eta * max_value
                noise = torch.normal(0, std, size=weight_copy.size())
                if weight_copy.is_cuda:
                    noise = noise.cuda()
                module.weight.data = noise + weight_copy
        elif isinstance(module, nn.Linear):
            with torch.no_grad():
                weight_copy = module.weight.data.clone()
                max_value = torch.max(weight_copy)
                std = eta * max_value
                noise = torch.normal(0, std, size=weight_copy.size())
                if weight_copy.is_cuda:
                    noise = noise.cuda()
                module.weight.data = noise + weight_copy
        elif isinstance(module, nn.Module):
            add_pcm_noise(module, eta=eta)

def main():
    args = parse_args()

    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f'Using device: {device}')

    # Load test data and determine model path if using default
    if args.dataset == 'cifar10':
        print('Loading CIFAR-10 test data...')
        test_loader = torch.utils.data.DataLoader(
            datasets.CIFAR10('./data.cifar10', train=False, download=True,
                           transform=transforms.Compose([
                               transforms.ToTensor(),
                               transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2023, 0.1994, 0.2010))
                           ])),
            batch_size=args.batch_size, shuffle=False, num_workers=1)
        num_classes = 10
        # Use CIFAR-10 model path if using default
        if args.model_path == 'checkpoint_NC/rram/cifar10/vgg16_nonid/ckpt_cifar10_nonid_0.0.pth':
            model_path = args.model_path
        else:
            model_path = args.model_path
    else:  # cifar100
        print('Loading CIFAR-100 test data...')
        test_loader = torch.utils.data.DataLoader(
            datasets.CIFAR100('./data.cifar100', train=False, download=True,
                           transform=transforms.Compose([
                               transforms.ToTensor(),
                               transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2023, 0.1994, 0.2010))
                           ])),
            batch_size=args.batch_size, shuffle=False, num_workers=1)
        num_classes = 100
        # Use CIFAR-100 model path if using default
        if args.model_path == 'checkpoint_NC/rram/cifar10/vgg16_nonid/ckpt_cifar10_nonid_0.0.pth':
            model_path = 'checkpoint_NC/cifar100/vgg16/ckpt_cifar100_nonid_0.0.pth'
        else:
            model_path = args.model_path

    # Load model
    print(f'Loading model from {model_path}')
    net = vgg16(num_classes=num_classes)
    net = net.to(device)
    
    if not os.path.exists(model_path):
        print(f"Error: Model file not found at {model_path}")
        if args.dataset == 'cifar100':
            print("You need to train the CIFAR-100 model first using:")
            print("python main_vgg.py --dataset cifar100 --model_name vgg16 --noise 0.0 --epoch 200 --lr 0.01 --batch_size 128")
        return

    checkpoint = torch.load(model_path, map_location=device)
    net.load_state_dict(checkpoint)
    
    # Add noise
    if args.noise_level > 0:
        print(f'Adding {args.noise_type} noise with level {args.noise_level}')
        if args.noise_type == 'rram':
            add_rram_noise(net, sigma=args.noise_level)
        else:
            add_pcm_noise(net, eta=args.noise_level)
    else:
        print('Testing without noise (baseline)')
    
    # Evaluate
    print('Evaluating model...')
    net.eval()
    criterion = nn.CrossEntropyLoss()
    test_loss = 0
    correct = 0
    total = 0
    
    with torch.no_grad():
        for batch_idx, (inputs, targets) in enumerate(test_loader):
            inputs, targets = inputs.to(device), targets.to(device)
            outputs = net(inputs)
            loss = criterion(outputs, targets)
            
            test_loss += loss.item()
            _, predicted = outputs.max(1)
            total += targets.size(0)
            correct += predicted.eq(targets).sum().item()
            
            if batch_idx % 50 == 0:
                progress_bar(batch_idx, len(test_loader), 'Loss: %.3f | Acc: %.3f%% (%d/%d)'
                           % (test_loss / (batch_idx + 1), 100. * correct / total, correct, total))
    
    accuracy = 100. * correct / total
    print(f'\nFinal Results:')
    print(f'Noise Type: {args.noise_type}')
    print(f'Noise Level: {args.noise_level}')
    print(f'Accuracy: {accuracy:.2f}%')

if __name__ == '__main__':
    main()
