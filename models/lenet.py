'''LeNet in PyTorch.'''
import torch.nn as nn
import torch.nn.functional as F
import torch

class LeNet(nn.Module):
    def __init__(self):
        super(LeNet, self).__init__()
        self.feature = nn.Sequential(
            nn.Conv2d(3, 6, 5),
            nn.<PERSON><PERSON><PERSON>(True),
            nn.<PERSON><PERSON>ool2d(2, 2),

            nn.Conv2d(6, 16, 5),
            nn.<PERSON><PERSON><PERSON>(True),
            nn.MaxPool2d(2, 2)
        )
        self.classifier = nn.Sequential(
            nn.Linear(16 * 5 * 5, 120),
            nn.<PERSON><PERSON><PERSON>(True),
            nn.<PERSON>ar(120, 84),
            nn.<PERSON><PERSON><PERSON>(True),
            nn.Linear(84, 10),
        )

    def forward(self, x):
        x = self.feature(x)
        x = torch.flatten(x, 1)
        x = self.classifier(x)

        return x
