import torch.nn as nn
import torch
import math
import torch.nn.functional as F

cfgs = [64, 64, 128, 128, 256, 256, 256, 512, 512, 512, 512, 512, 512, 256, 128]


class vgg16(nn.Module):
    def __init__(self,num_classes=10, cfg = cfgs):
        super(vgg16, self).__init__()
        self.relu = nn.ReLU(inplace=True)
        self.maxpool2d = nn.MaxPool2d(kernel_size=2, stride=2)
        self.conv1 = nn.Conv2d(3, cfg[0], kernel_size=3, stride=1, padding=1)
        self.bn1 = nn.BatchNorm2d(cfg[0])
        self.conv2 = nn.Conv2d(cfg[0], cfg[1], kernel_size=3, stride=1, padding=1)
        self.bn2 = nn.BatchNorm2d(cfg[1])

        self.conv3 = nn.Conv2d(cfg[1], cfg[2], kernel_size=3, stride=1, padding=1)
        self.bn3 = nn.BatchNorm2d(cfg[2])
        self.conv4 = nn.Conv2d(cfg[2], cfg[3], kernel_size=3, stride=1, padding=1)
        self.bn4 = nn.BatchNorm2d(cfg[3])

        self.conv5 = nn.Conv2d(cfg[3], cfg[4], kernel_size=3, stride=1, padding=1)
        self.bn5 = nn.BatchNorm2d(cfg[4])
        self.conv6 = nn.Conv2d(cfg[4], cfg[5], kernel_size=3, stride=1, padding=1)
        self.bn6 = nn.BatchNorm2d(cfg[5])
        self.conv7 = nn.Conv2d(cfg[5], cfg[6], kernel_size=3, stride=1, padding=1)
        self.bn7 = nn.BatchNorm2d(cfg[6])

        self.conv8 = nn.Conv2d(cfg[6], cfg[7], kernel_size=3, stride=1, padding=1)
        self.bn8 = nn.BatchNorm2d(cfg[7])
        self.conv9 = nn.Conv2d(cfg[7], cfg[8], kernel_size=3, stride=1, padding=1)
        self.bn9 = nn.BatchNorm2d(cfg[8])
        self.conv10 = nn.Conv2d(cfg[8], cfg[9], kernel_size=3, stride=1, padding=1)
        self.bn10 = nn.BatchNorm2d(cfg[9])

        self.conv11 = nn.Conv2d(cfg[9], cfg[10], kernel_size=3, stride=1, padding=1)
        self.bn11 = nn.BatchNorm2d(cfg[10])
        self.conv12 = nn.Conv2d(cfg[10], cfg[11], kernel_size=3, stride=1, padding=1)
        self.bn12 = nn.BatchNorm2d(cfg[11])
        self.conv13 = nn.Conv2d(cfg[11], cfg[12], kernel_size=3, stride=1, padding=1)
        self.bn13 = nn.BatchNorm2d(cfg[12])

        self.lonear1 = nn.Linear(cfg[12], cfg[13])
        self.lbn1 = nn.BatchNorm1d(cfg[13])
        self.lonear2 = nn.Linear(cfg[13], cfg[14])
        self.lbn2 = nn.BatchNorm1d(cfg[14])
        self.lonear3 = nn.Linear(cfg[14], num_classes)

        #self._initialize_weights()
        

    def forward(self, x):
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)
        x = self.conv2(x)
        x = self.bn2(x)
        x = self.relu(x)
        x = self.maxpool2d(x)

        x = self.conv3(x)
        x = self.bn3(x)
        x = self.relu(x)
        x = self.conv4(x)
        x = self.bn4(x)
        x = self.relu(x)
        x = self.maxpool2d(x)

        x = self.conv5(x)
        x = self.bn5(x)
        x = self.relu(x)
        x = self.conv6(x)
        x = self.bn6(x)
        x = self.relu(x)
        x = self.conv7(x)
        x = self.bn7(x)
        x = self.relu(x)
        x = self.maxpool2d(x)

        x = self.conv8(x)
        x = self.bn8(x)
        x = self.relu(x)
        x = self.conv9(x)
        x = self.bn9(x)
        x = self.relu(x)
        x = self.conv10(x)
        x = self.bn10(x)
        x = self.relu(x)
        x = self.maxpool2d(x)

        x = self.conv11(x)
        x = self.bn11(x)
        x = self.relu(x)
        x = self.conv12(x)
        x = self.bn12(x)
        x = self.relu(x)
        x = self.conv13(x)
        x = self.bn13(x)
        x = self.relu(x)
        x = nn.AvgPool2d(2)(x)

        x = torch.flatten(x, 1)
        
        x = self.lonear1(x)
        x = self.lbn1(x)
        x = self.relu(x)
        x = self.lonear2(x)
        x = self.lbn2(x)
        x = self.relu(x)
        x = self.lonear3(x)
        
        return x


class vgg16_quant(nn.Module):
    def __init__(self,num_classes=10, cfg = cfgs):
        super(vgg16_quant, self).__init__()
        self.relu = nn.ReLU(inplace=True)
        self.maxpool2d = nn.MaxPool2d(kernel_size=2, stride=2)
        self.conv1 = nn.Conv2d(3, cfg[0], kernel_size=3, stride=1, padding=1)
        self.bn1 = nn.BatchNorm2d(cfg[0])
        self.conv2 = nn.Conv2d(cfg[0], cfg[1], kernel_size=3, stride=1, padding=1)
        self.bn2 = nn.BatchNorm2d(cfg[1])

        self.conv3 = nn.Conv2d(cfg[1], cfg[2], kernel_size=3, stride=1, padding=1)
        self.bn3 = nn.BatchNorm2d(cfg[2])
        self.conv4 = nn.Conv2d(cfg[2], cfg[3], kernel_size=3, stride=1, padding=1)
        self.bn4 = nn.BatchNorm2d(cfg[3])

        self.conv5 = nn.Conv2d(cfg[3], cfg[4], kernel_size=3, stride=1, padding=1)
        self.bn5 = nn.BatchNorm2d(cfg[4])
        self.conv6 = nn.Conv2d(cfg[4], cfg[5], kernel_size=3, stride=1, padding=1)
        self.bn6 = nn.BatchNorm2d(cfg[5])
        self.conv7 = nn.Conv2d(cfg[5], cfg[6], kernel_size=3, stride=1, padding=1)
        self.bn7 = nn.BatchNorm2d(cfg[6])

        self.conv8 = nn.Conv2d(cfg[6], cfg[7], kernel_size=3, stride=1, padding=1)
        self.bn8 = nn.BatchNorm2d(cfg[7])
        self.conv9 = nn.Conv2d(cfg[7], cfg[8], kernel_size=3, stride=1, padding=1)
        self.bn9 = nn.BatchNorm2d(cfg[8])
        self.conv10 = nn.Conv2d(cfg[8], cfg[9], kernel_size=3, stride=1, padding=1)
        self.bn10 = nn.BatchNorm2d(cfg[9])

        self.conv11 = nn.Conv2d(cfg[9], cfg[10], kernel_size=3, stride=1, padding=1)
        self.bn11 = nn.BatchNorm2d(cfg[10])
        self.conv12 = nn.Conv2d(cfg[10], cfg[11], kernel_size=3, stride=1, padding=1)
        self.bn12 = nn.BatchNorm2d(cfg[11])
        self.conv13 = nn.Conv2d(cfg[11], cfg[12], kernel_size=3, stride=1, padding=1)
        self.bn13 = nn.BatchNorm2d(cfg[12])

        self.lonear1 = nn.Linear(cfg[12], cfg[13])
        self.lbn1 = nn.BatchNorm1d(cfg[13])
        self.lonear2 = nn.Linear(cfg[13], cfg[14])
        self.lbn2 = nn.BatchNorm1d(cfg[14])
        self.lonear3 = nn.Linear(cfg[14], num_classes)
        self.quant = torch.ao.quantization.QuantStub()
        self.dequant = torch.ao.quantization.DeQuantStub()

        self._initialize_weights()
        

    def forward(self, x):
        x = self.quant(x)
        x = self.conv1(x)
        x = self.bn1(x)
        x = self.relu(x)
        x = self.conv2(x)
        x = self.bn2(x)
        x = self.relu(x)
        x = self.maxpool2d(x)

        x = self.conv3(x)
        x = self.bn3(x)
        x = self.relu(x)
        x = self.conv4(x)
        x = self.bn4(x)
        x = self.relu(x)
        x = self.maxpool2d(x)

        x = self.conv5(x)
        x = self.bn5(x)
        x = self.relu(x)
        x = self.conv6(x)
        x = self.bn6(x)
        x = self.relu(x)
        x = self.conv7(x)
        x = self.bn7(x)
        x = self.relu(x)
        x = self.maxpool2d(x)

        x = self.conv8(x)
        x = self.bn8(x)
        x = self.relu(x)
        x = self.conv9(x)
        x = self.bn9(x)
        x = self.relu(x)
        x = self.conv10(x)
        x = self.bn10(x)
        x = self.relu(x)
        x = self.maxpool2d(x)

        x = self.conv11(x)
        x = self.bn11(x)
        x = self.relu(x)
        x = self.conv12(x)
        x = self.bn12(x)
        x = self.relu(x)
        x = self.conv13(x)
        x = self.bn13(x)
        x = self.relu(x)
        x = nn.AvgPool2d(2)(x)

        x = torch.flatten(x, 1)
        
        x = self.lonear1(x)
        x = self.dequant(x)


        x = self.lbn1(x)

        x = self.quant(x)
        x = self.relu(x)
        x = self.lonear2(x)
        x = self.dequant(x)


        x = self.lbn2(x)

        x = self.quant(x)
        x = self.relu(x)
        x = self.lonear3(x)
        x = self.dequant(x)
        

        return x
    def _initialize_weights(self):
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                n = m.kernel_size[0] * m.kernel_size[1] * m.out_channels
                m.weight.data.normal_(0, math.sqrt(2. / n))
                if m.bias is not None:
                    m.bias.data.zero_()
            elif isinstance(m, nn.BatchNorm2d):
                m.weight.data.fill_(0.5)
                m.bias.data.zero_()
            elif isinstance(m, nn.Linear):
                m.weight.data.normal_(0, 0.01)
                m.bias.data.zero_()


#layer_id = 0

class vgg16_nonid(nn.Module):
    def __init__(self,num_classes=10, cfg = cfgs):
        super(vgg16_nonid, self).__init__()
        self.relu = nn.ReLU(inplace=True)
        self.maxpool2d = nn.MaxPool2d(kernel_size=2, stride=2)
        self.conv1 = nn.Conv2d(3, cfg[0], kernel_size=3, stride=1, padding=1)
        self.bn1 = nn.BatchNorm2d(cfg[0])
        self.conv2 = nn.Conv2d(cfg[0], cfg[1], kernel_size=3, stride=1, padding=1)
        self.bn2 = nn.BatchNorm2d(cfg[1])

        self.conv3 = nn.Conv2d(cfg[1], cfg[2], kernel_size=3, stride=1, padding=1)
        self.bn3 = nn.BatchNorm2d(cfg[2])
        self.conv4 = nn.Conv2d(cfg[2], cfg[3], kernel_size=3, stride=1, padding=1)
        self.bn4 = nn.BatchNorm2d(cfg[3])

        self.conv5 = nn.Conv2d(cfg[3], cfg[4], kernel_size=3, stride=1, padding=1)
        self.bn5 = nn.BatchNorm2d(cfg[4])
        self.conv6 = nn.Conv2d(cfg[4], cfg[5], kernel_size=3, stride=1, padding=1)
        self.bn6 = nn.BatchNorm2d(cfg[5])
        self.conv7 = nn.Conv2d(cfg[5], cfg[6], kernel_size=3, stride=1, padding=1)
        self.bn7 = nn.BatchNorm2d(cfg[6])

        self.conv8 = nn.Conv2d(cfg[6], cfg[7], kernel_size=3, stride=1, padding=1)
        self.bn8 = nn.BatchNorm2d(cfg[7])
        self.conv9 = nn.Conv2d(cfg[7], cfg[8], kernel_size=3, stride=1, padding=1)
        self.bn9 = nn.BatchNorm2d(cfg[8])
        self.conv10 = nn.Conv2d(cfg[8], cfg[9], kernel_size=3, stride=1, padding=1)
        self.bn10 = nn.BatchNorm2d(cfg[9])

        self.conv11 = nn.Conv2d(cfg[9], cfg[10], kernel_size=3, stride=1, padding=1)
        self.bn11 = nn.BatchNorm2d(cfg[10])
        self.conv12 = nn.Conv2d(cfg[10], cfg[11], kernel_size=3, stride=1, padding=1)
        self.bn12 = nn.BatchNorm2d(cfg[11])
        self.conv13 = nn.Conv2d(cfg[11], cfg[12], kernel_size=3, stride=1, padding=1)
        self.bn13 = nn.BatchNorm2d(cfg[12])

        self.lonear1 = nn.Linear(cfg[12], cfg[13])
        self.lbn1 = nn.BatchNorm1d(cfg[13])
        self.lonear2 = nn.Linear(cfg[13], cfg[14])
        self.lbn2 = nn.BatchNorm1d(cfg[14])
        self.lonear3 = nn.Linear(cfg[14], num_classes)

        

    def forward(self, x, lamda=0.5):
        feature_map = []
        # noise1 = self.conv1.weight*torch.normal(0., torch.full(self.conv1.weight.size(),lamda)).cuda()
        # x = F.conv2d(input=x, weight=noise1 + self.conv1.weight, bias=self.conv1.bias, stride=1, padding=1)
        noise1 = torch.normal(0, lamda, size=self.conv1.weight.size()).cuda()
        x = F.conv2d(input=x, weight=torch.exp(noise1) * self.conv1.weight, bias=self.conv1.bias, stride=1, padding=1)
        feature_map.append(x)
        #x = self.conv1(x)
        x = self.bn1(x)
        #feature_map.append(x)
        x = self.relu(x)
        noise2 = torch.normal(0, lamda, size=self.conv2.weight.size()).cuda()
        x = F.conv2d(input=x, weight=torch.exp(noise2) * self.conv2.weight, bias=self.conv2.bias, stride=1, padding=1)
        feature_map.append(x)
        #x = self.conv2(x)
        x = self.bn2(x)
        #feature_map.append(x)
        x = self.relu(x)
        x = self.maxpool2d(x)

        noise3 = torch.normal(0, lamda, size=self.conv3.weight.size()).cuda()
        x = F.conv2d(input=x, weight=torch.exp(noise3) * self.conv3.weight, bias=self.conv3.bias, stride=1, padding=1)
        feature_map.append(x)
        #x = self.conv3(x)
        x = self.bn3(x)
        #feature_map.append(x)
        x = self.relu(x)
        noise4 = torch.normal(0, lamda, size=self.conv4.weight.size()).cuda()
        x = F.conv2d(input=x, weight=torch.exp(noise4) * self.conv4.weight, bias=self.conv4.bias, stride=1, padding=1)
        feature_map.append(x)
        #x = self.conv4(x)
        x = self.bn4(x)
        #feature_map.append(x)
        x = self.relu(x)
        x = self.maxpool2d(x)

        noise5 = torch.normal(0, lamda, size=self.conv5.weight.size()).cuda()
        x = F.conv2d(input=x, weight=torch.exp(noise5) * self.conv5.weight, bias=self.conv5.bias, stride=1, padding=1)
        feature_map.append(x)
        #x = self.conv5(x)
        x = self.bn5(x)
        #feature_map.append(x)
        x = self.relu(x)
        noise6 = torch.normal(0, lamda, size=self.conv6.weight.size()).cuda()
        x = F.conv2d(input=x, weight=torch.exp(noise6) * self.conv6.weight, bias=self.conv6.bias, stride=1, padding=1)
        feature_map.append(x)
        #x = self.conv6(x)
        x = self.bn6(x)
        #feature_map.append(x)
        x = self.relu(x)
        noise7 = torch.normal(0, lamda, size=self.conv7.weight.size()).cuda()
        x = F.conv2d(input=x, weight=torch.exp(noise7) * self.conv7.weight, bias=self.conv7.bias, stride=1, padding=1)
        feature_map.append(x)
        #x = self.conv7(x)
        x = self.bn7(x)
        #feature_map.append(x)
        x = self.relu(x)
        x = self.maxpool2d(x)

        noise8 = torch.normal(0, lamda, size=self.conv8.weight.size()).cuda()
        x = F.conv2d(input=x, weight=torch.exp(noise8) * self.conv8.weight, bias=self.conv8.bias, stride=1, padding=1)
        feature_map.append(x)
        #x = self.conv8(x)
        x = self.bn8(x)
        #feature_map.append(x)
        x = self.relu(x)
        noise9 = torch.normal(0, lamda, size=self.conv9.weight.size()).cuda()
        x = F.conv2d(input=x, weight=torch.exp(noise9) * self.conv9.weight, bias=self.conv9.bias, stride=1, padding=1)
        feature_map.append(x)
        #x = self.conv9(x)
        x = self.bn9(x)
        #feature_map.append(x)
        x = self.relu(x)
        noise10 = torch.normal(0, lamda, size=self.conv10.weight.size()).cuda()
        x = F.conv2d(input=x, weight=torch.exp(noise10) * self.conv10.weight, bias=self.conv10.bias, stride=1, padding=1)
        feature_map.append(x)
        #x = self.conv10(x)
        x = self.bn10(x)
        #feature_map.append(x)
        x = self.relu(x)
        x = self.maxpool2d(x)

        noise11 = torch.normal(0, lamda, size=self.conv11.weight.size()).cuda()
        x = F.conv2d(input=x, weight=torch.exp(noise11) * self.conv11.weight, bias=self.conv11.bias, stride=1, padding=1)
        feature_map.append(x)
        #x = self.conv11(x)
        x = self.bn11(x)
        #feature_map.append(x)
        x = self.relu(x)
        noise12 = torch.normal(0, lamda, size=self.conv12.weight.size()).cuda()
        x = F.conv2d(input=x, weight=torch.exp(noise12) * self.conv12.weight, bias=self.conv12.bias, stride=1, padding=1)
        feature_map.append(x)
        #x = self.conv12(x)
        x = self.bn12(x)
        #feature_map.append(x)
        x = self.relu(x)
        noise13 = torch.normal(0, lamda, size=self.conv13.weight.size()).cuda()
        x = F.conv2d(input=x, weight=torch.exp(noise13) * self.conv13.weight, bias=self.conv13.bias, stride=1, padding=1)
        feature_map.append(x)
        #x = self.conv13(x)
        x = self.bn13(x)
        #feature_map.append(x)
        x = self.relu(x)
        #x = nn.AvgPool2d(2)(x)
        x = nn.AdaptiveAvgPool2d((1,1))(x)

        x = torch.flatten(x, 1)
        
        l_noise1 = torch.normal(0, lamda, size=self.lonear1.weight.size()).cuda()
        x = F.linear(input=x, weight=torch.exp(l_noise1) * self.lonear1.weight, bias=self.lonear1.bias)
        #feature_map.append(x)
        #x = self.lonear1(x)
        x = self.lbn1(x)
        #feature_map.append(x)
        x = self.relu(x)
        l_noise2 = torch.normal(0, lamda, size=self.lonear2.weight.size()).cuda()
        x = F.linear(input=x, weight=torch.exp(l_noise2) * self.lonear2.weight, bias=self.lonear2.bias)
        #feature_map.append(x)
        #x = self.lonear2(x)
        x = self.lbn2(x)
        #feature_map.append(x)
        x = self.relu(x)
        l_noise3 = torch.normal(0, lamda, size=self.lonear3.weight.size()).cuda()
        x = F.linear(input=x, weight=torch.exp(l_noise3) * self.lonear3.weight, bias=self.lonear3.bias)
        #feature_map.append(x)
        #x = self.lonear3(x)

        return x#, feature_map
    

class vgg16_nonid_pcm(nn.Module):
    def __init__(self,num_classes=10, cfg = cfgs):
        super(vgg16_nonid_pcm, self).__init__()
        self.relu = nn.ReLU(inplace=True)
        self.maxpool2d = nn.MaxPool2d(kernel_size=2, stride=2)
        self.conv1 = nn.Conv2d(3, cfg[0], kernel_size=3, stride=1, padding=1)
        self.bn1 = nn.BatchNorm2d(cfg[0])
        self.conv2 = nn.Conv2d(cfg[0], cfg[1], kernel_size=3, stride=1, padding=1)
        self.bn2 = nn.BatchNorm2d(cfg[1])

        self.conv3 = nn.Conv2d(cfg[1], cfg[2], kernel_size=3, stride=1, padding=1)
        self.bn3 = nn.BatchNorm2d(cfg[2])
        self.conv4 = nn.Conv2d(cfg[2], cfg[3], kernel_size=3, stride=1, padding=1)
        self.bn4 = nn.BatchNorm2d(cfg[3])

        self.conv5 = nn.Conv2d(cfg[3], cfg[4], kernel_size=3, stride=1, padding=1)
        self.bn5 = nn.BatchNorm2d(cfg[4])
        self.conv6 = nn.Conv2d(cfg[4], cfg[5], kernel_size=3, stride=1, padding=1)
        self.bn6 = nn.BatchNorm2d(cfg[5])
        self.conv7 = nn.Conv2d(cfg[5], cfg[6], kernel_size=3, stride=1, padding=1)
        self.bn7 = nn.BatchNorm2d(cfg[6])

        self.conv8 = nn.Conv2d(cfg[6], cfg[7], kernel_size=3, stride=1, padding=1)
        self.bn8 = nn.BatchNorm2d(cfg[7])
        self.conv9 = nn.Conv2d(cfg[7], cfg[8], kernel_size=3, stride=1, padding=1)
        self.bn9 = nn.BatchNorm2d(cfg[8])
        self.conv10 = nn.Conv2d(cfg[8], cfg[9], kernel_size=3, stride=1, padding=1)
        self.bn10 = nn.BatchNorm2d(cfg[9])

        self.conv11 = nn.Conv2d(cfg[9], cfg[10], kernel_size=3, stride=1, padding=1)
        self.bn11 = nn.BatchNorm2d(cfg[10])
        self.conv12 = nn.Conv2d(cfg[10], cfg[11], kernel_size=3, stride=1, padding=1)
        self.bn12 = nn.BatchNorm2d(cfg[11])
        self.conv13 = nn.Conv2d(cfg[11], cfg[12], kernel_size=3, stride=1, padding=1)
        self.bn13 = nn.BatchNorm2d(cfg[12])

        self.lonear1 = nn.Linear(cfg[12], cfg[13])
        self.lbn1 = nn.BatchNorm1d(cfg[13])
        self.lonear2 = nn.Linear(cfg[13], cfg[14])
        self.lbn2 = nn.BatchNorm1d(cfg[14])
        self.lonear3 = nn.Linear(cfg[14], num_classes)

        

    def forward(self, x, lamda=0.5):
        feature_map = []
        # noise1 = self.conv1.weight*torch.normal(0., torch.full(self.conv1.weight.size(),lamda)).cuda()
        # x = F.conv2d(input=x, weight=noise1 + self.conv1.weight, bias=self.conv1.bias, stride=1, padding=1)
        weight_copy =self.conv1.weight.clone() 
        max_value = torch.max(weight_copy)
        std = torch.tensor(lamda * max_value)
        noise1 = torch.normal(0, std, size=weight_copy.size()).cuda()    
        x = F.conv2d(input=x, weight=noise1 + self.conv1.weight, bias=self.conv1.bias, stride=1, padding=1)
        feature_map.append(x)
        #x = self.conv1(x)
        x = self.bn1(x)
        #feature_map.append(x)
        x = self.relu(x)
        weight_copy =self.conv2.weight.clone() 
        max_value = torch.max(weight_copy)
        std = torch.tensor(lamda * max_value)
        noise2 = torch.normal(0, std, size=weight_copy.size()).cuda()
        x = F.conv2d(input=x, weight=noise2 + self.conv2.weight, bias=self.conv2.bias, stride=1, padding=1)
        feature_map.append(x)
        #x = self.conv2(x)
        x = self.bn2(x)
        #feature_map.append(x)
        x = self.relu(x)
        x = self.maxpool2d(x)

        weight_copy =self.conv3.weight.clone() 
        max_value = torch.max(weight_copy)
        std = torch.tensor(lamda * max_value)
        noise3 = torch.normal(0, std, size=weight_copy.size()).cuda()
        x = F.conv2d(input=x, weight=noise3 + self.conv3.weight, bias=self.conv3.bias, stride=1, padding=1)
        feature_map.append(x)
        #x = self.conv3(x)
        x = self.bn3(x)
        #feature_map.append(x)
        x = self.relu(x)
        weight_copy =self.conv4.weight.clone() 
        max_value = torch.max(weight_copy)
        std = torch.tensor(lamda * max_value)
        noise4 = torch.normal(0, std, size=weight_copy.size()).cuda()
        x = F.conv2d(input=x, weight= noise4 + self.conv4.weight, bias=self.conv4.bias, stride=1, padding=1)
        feature_map.append(x)
        #x = self.conv4(x)
        x = self.bn4(x)
        #feature_map.append(x)
        x = self.relu(x)
        x = self.maxpool2d(x)

        weight_copy =self.conv5.weight.clone() 
        max_value = torch.max(weight_copy)
        std = torch.tensor(lamda * max_value)
        noise5 = torch.normal(0, std, size=weight_copy.size()).cuda()
        x = F.conv2d(input=x, weight= noise5 + self.conv5.weight, bias=self.conv5.bias, stride=1, padding=1)
        feature_map.append(x)
        #x = self.conv5(x)
        x = self.bn5(x)
        #feature_map.append(x)
        x = self.relu(x)
        weight_copy =self.conv6.weight.clone() 
        max_value = torch.max(weight_copy)
        std = torch.tensor(lamda * max_value)
        noise6 = torch.normal(0, std, size=weight_copy.size()).cuda()
        x = F.conv2d(input=x, weight= noise6 + self.conv6.weight, bias=self.conv6.bias, stride=1, padding=1)
        feature_map.append(x)
        #x = self.conv6(x)
        x = self.bn6(x)
        #feature_map.append(x)
        x = self.relu(x)
        weight_copy =self.conv7.weight.clone() 
        max_value = torch.max(weight_copy)
        std = torch.tensor(lamda * max_value)
        noise7 = torch.normal(0, std, size=weight_copy.size()).cuda()
        x = F.conv2d(input=x, weight= noise7 + self.conv7.weight, bias=self.conv7.bias, stride=1, padding=1)
        feature_map.append(x)
        #x = self.conv7(x)
        x = self.bn7(x)
        #feature_map.append(x)
        x = self.relu(x)
        x = self.maxpool2d(x)

        weight_copy =self.conv8.weight.clone() 
        max_value = torch.max(weight_copy)
        std = torch.tensor(lamda * max_value)
        noise8 = torch.normal(0, std, size=weight_copy.size()).cuda()
        x = F.conv2d(input=x, weight= noise8 + self.conv8.weight, bias=self.conv8.bias, stride=1, padding=1)
        feature_map.append(x)
        #x = self.conv8(x)
        x = self.bn8(x)
        #feature_map.append(x)
        x = self.relu(x)
        weight_copy =self.conv9.weight.clone() 
        max_value = torch.max(weight_copy)
        std = torch.tensor(lamda * max_value)
        noise9 = torch.normal(0, std, size=weight_copy.size()).cuda()
        x = F.conv2d(input=x, weight= noise9 + self.conv9.weight, bias=self.conv9.bias, stride=1, padding=1)
        feature_map.append(x)
        #x = self.conv9(x)
        x = self.bn9(x)
        #feature_map.append(x)
        x = self.relu(x)
        weight_copy =self.conv10.weight.clone() 
        max_value = torch.max(weight_copy)
        std = torch.tensor(lamda * max_value)
        noise10 = torch.normal(0, std, size=weight_copy.size()).cuda()
        x = F.conv2d(input=x, weight= noise10 + self.conv10.weight, bias=self.conv10.bias, stride=1, padding=1)
        feature_map.append(x)
        #x = self.conv10(x)
        x = self.bn10(x)
        #feature_map.append(x)
        x = self.relu(x)
        x = self.maxpool2d(x)

        weight_copy =self.conv11.weight.clone() 
        max_value = torch.max(weight_copy)
        std = torch.tensor(lamda * max_value)
        noise11 = torch.normal(0, std, size=weight_copy.size()).cuda()
        x = F.conv2d(input=x, weight= noise11 + self.conv11.weight, bias=self.conv11.bias, stride=1, padding=1)
        feature_map.append(x)
        #x = self.conv11(x)
        x = self.bn11(x)
        #feature_map.append(x)
        x = self.relu(x)
        weight_copy =self.conv12.weight.clone() 
        max_value = torch.max(weight_copy)
        std = torch.tensor(lamda * max_value)
        noise12 = torch.normal(0, std, size=weight_copy.size()).cuda()
        x = F.conv2d(input=x, weight= noise12 + self.conv12.weight, bias=self.conv12.bias, stride=1, padding=1)
        feature_map.append(x)
        #x = self.conv12(x)
        x = self.bn12(x)
        #feature_map.append(x)
        x = self.relu(x)
        weight_copy =self.conv13.weight.clone() 
        max_value = torch.max(weight_copy)
        std = torch.tensor(lamda * max_value)
        noise13 = torch.normal(0, std, size=weight_copy.size()).cuda()
        x = F.conv2d(input=x, weight= noise13 + self.conv13.weight, bias=self.conv13.bias, stride=1, padding=1)
        feature_map.append(x)
        #x = self.conv13(x)
        x = self.bn13(x)
        #feature_map.append(x)
        x = self.relu(x)
        #x = nn.AvgPool2d(2)(x)
        x = nn.AdaptiveAvgPool2d((1,1))(x)

        x = torch.flatten(x, 1)
        weight_copy =self.lonear1.weight.clone() 
        max_value = torch.max(weight_copy)
        std = torch.tensor(lamda * max_value)
        l_noise1 = torch.normal(0, std, size=weight_copy.size()).cuda()
        x = F.linear(input=x, weight= l_noise1 + self.lonear1.weight, bias=self.lonear1.bias)
        #feature_map.append(x)
        #x = self.lonear1(x)
        x = self.lbn1(x)
        #feature_map.append(x)
        x = self.relu(x)
        weight_copy =self.lonear2.weight.clone() 
        max_value = torch.max(weight_copy)
        std = torch.tensor(lamda * max_value)
        l_noise2 = torch.normal(0, std, size=weight_copy.size()).cuda()
        x = F.linear(input=x, weight= l_noise2 + self.lonear2.weight, bias=self.lonear2.bias)
        #feature_map.append(x)
        #x = self.lonear2(x)
        x = self.lbn2(x)
        #feature_map.append(x)
        x = self.relu(x)
        weight_copy =self.lonear3.weight.clone() 
        max_value = torch.max(weight_copy)
        std = torch.tensor(lamda * max_value)
        l_noise3 = torch.normal(0, std, size=weight_copy.size()).cuda()
        x = F.linear(input=x, weight= l_noise3 + self.lonear3.weight, bias=self.lonear3.bias)
        #feature_map.append(x)
        #x = self.lonear3(x)

        return x#, feature_map
    
