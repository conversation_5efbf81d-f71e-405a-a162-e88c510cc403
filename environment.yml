name: nonid
channels:
  - pytorch
  - nvidia
  - https://repo.anaconda.com/pkgs/main
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1=conda_forge
  - _openmp_mutex=4.5=2_gnu
  - anaconda-anon-usage=0.4.4=py312hfc0e8ea_100
  - archspec=0.2.3=pyhd3eb1b0_0
  - asttokens=3.0.0=pyhd8ed1ab_1
  - atk-1.0=2.36.0=ha1a6a79_0
  - blas=1.0=mkl
  - boltons=23.0.0=py312h06a4308_0
  - boost-cpp=1.82.0=hdb19cb5_2
  - brotli-python=1.0.9=py312h6a678d5_8
  - bzip2=1.0.8=h5eee18b_6
  - c-ares=1.19.1=h5eee18b_0
  - ca-certificates=2025.2.25=h06a4308_0
  - cairo=1.16.0=hb05425b_5
  - certifi=2025.1.31=py312h06a4308_0
  - cffi=1.16.0=py312h5eee18b_1
  - charset-normalizer=2.0.4=pyhd3eb1b0_0
  - comm=0.2.2=pyhd8ed1ab_1
  - conda=24.11.3=py312h06a4308_0
  - conda-content-trust=0.2.0=py312h06a4308_1
  - conda-libmamba-solver=24.1.0=pyhd3eb1b0_0
  - conda-package-handling=2.2.0=py312h06a4308_1
  - conda-package-streaming=0.9.0=py312h06a4308_0
  - cryptography=42.0.5=py312hdda0065_1
  - cuda-cudart=12.1.105=0
  - cuda-cupti=12.1.105=0
  - cuda-libraries=12.1.0=0
  - cuda-nvrtc=12.1.105=0
  - cuda-nvtx=12.1.105=0
  - cuda-opencl=12.5.39=0
  - cuda-runtime=12.1.0=0
  - cuda-version=12.5=3
  - debugpy=1.8.11=py312h6a678d5_0
  - decorator=5.1.1=pyhd8ed1ab_1
  - distro=1.9.0=py312h06a4308_0
  - exceptiongroup=1.2.2=pyhd8ed1ab_1
  - executing=2.1.0=pyhd8ed1ab_1
  - expat=2.6.2=h6a678d5_0
  - faiss-gpu=1.8.0=py3.12_h4c7d538_0_cuda12.1.1
  - ffmpeg=4.3=hf484d3e_0
  - filelock=3.13.1=py312h06a4308_0
  - fmt=9.1.0=hdb19cb5_1
  - font-ttf-dejavu-sans-mono=2.37=hd3eb1b0_0
  - font-ttf-inconsolata=2.001=hcb22688_0
  - font-ttf-source-code-pro=2.030=hd3eb1b0_0
  - font-ttf-ubuntu=0.83=h8b1ccd4_0
  - fontconfig=2.14.1=h4c34cd2_2
  - fonts-anaconda=1=h8fa9717_0
  - fonts-conda-ecosystem=1=hd3eb1b0_0
  - freetype=2.12.1=h4a9f257_0
  - fribidi=1.0.10=h7b6447c_0
  - frozendict=2.4.2=py312h06a4308_0
  - gdk-pixbuf=2.42.10=h5eee18b_1
  - giflib=5.2.2=h5eee18b_0
  - glib=2.78.4=h6a678d5_0
  - glib-tools=2.78.4=h6a678d5_0
  - gmp=6.2.1=h295c915_3
  - gnutls=3.6.15=he1e5248_0
  - gobject-introspection=1.78.1=py312h42194e9_2
  - graphite2=1.3.14=h295c915_1
  - gtk2=2.24.33=h27e1c3a_3
  - gts=0.7.6=hb67d8dd_3
  - harfbuzz=4.3.0=hf52aaf7_2
  - icu=73.1=h6a678d5_0
  - idna=3.7=py312h06a4308_0
  - importlib-metadata=8.5.0=pyha770c72_1
  - intel-openmp=2023.1.0=hdb19cb5_46306
  - ipykernel=6.29.5=pyh3099207_0
  - ipython=8.30.0=pyh707e725_0
  - jedi=0.19.2=pyhd8ed1ab_1
  - jinja2=3.1.4=py312h06a4308_0
  - jpeg=9e=h5eee18b_1
  - jsonpatch=1.33=py312h06a4308_1
  - jsonpointer=2.1=pyhd3eb1b0_0
  - jupyter_client=8.6.3=pyhd8ed1ab_1
  - jupyter_core=5.7.2=pyh31011fe_1
  - krb5=1.20.1=h143b758_1
  - lame=3.100=h7b6447c_0
  - lcms2=2.12=h3be6417_0
  - ld_impl_linux-64=2.38=h1181459_1
  - lerc=3.0=h295c915_0
  - libarchive=3.6.2=h6ac8c49_3
  - libboost=1.82.0=h109eef0_2
  - libcublas=*********=0
  - libcufft=********=0
  - libcufile=********=0
  - libcurand=*********=0
  - libcurl=8.7.1=h251f7ec_0
  - libcusolver=*********=0
  - libcusparse=*********=0
  - libdeflate=1.17=h5eee18b_1
  - libedit=3.1.20230828=h5eee18b_0
  - libev=4.33=h7f8727e_1
  - libexpat=2.6.2=h59595ed_0
  - libfaiss=1.8.0=h046e95b_0_cuda12.1.1
  - libffi=3.4.4=h6a678d5_1
  - libgcc=14.2.0=h77fa898_1
  - libgcc-ng=14.2.0=h69a702a_1
  - libgd=2.3.3=h695aa2c_1
  - libglib=2.78.4=hdc74915_0
  - libgomp=14.2.0=h77fa898_1
  - libiconv=1.16=h5eee18b_3
  - libidn2=2.3.4=h5eee18b_0
  - libjpeg-turbo=2.0.0=h9bf148f_0
  - libmamba=1.5.8=hfe524e5_2
  - libmambapy=1.5.8=py312h2dafd23_2
  - libnghttp2=1.57.0=h2d74bed_0
  - libnpp=12.0.2.50=0
  - libnsl=2.0.1=hd590300_0
  - libnvjitlink=12.1.105=0
  - libnvjpeg=12.1.1.14=0
  - libpng=1.6.39=h5eee18b_0
  - librsvg=2.54.4=h36cc946_3
  - libsodium=1.0.18=h36c2ea0_1
  - libsolv=0.7.24=he621ea3_1
  - libsqlite=3.46.0=hde9e2c9_0
  - libssh2=1.11.0=h251f7ec_0
  - libstdcxx=14.2.0=hc0a3c3a_1
  - libstdcxx-ng=11.2.0=h1234567_1
  - libtasn1=4.19.0=h5eee18b_0
  - libtiff=4.5.1=h6a678d5_0
  - libtool=2.4.7=h6a678d5_0
  - libunistring=0.9.10=h27cfd23_0
  - libuuid=1.41.5=h5eee18b_0
  - libwebp=1.3.2=h11a3e52_0
  - libwebp-base=1.3.2=h5eee18b_0
  - libxcb=1.15=h7f8727e_0
  - libxcrypt=4.4.36=hd590300_1
  - libxml2=2.10.4=hfdd30dd_2
  - libzlib=1.2.13=h4ab18f5_6
  - llvm-openmp=14.0.6=h9e868ea_0
  - lz4-c=1.9.4=h6a678d5_1
  - markupsafe=2.1.3=py312h5eee18b_0
  - matplotlib-inline=0.1.7=pyhd8ed1ab_1
  - menuinst=2.0.2=py312h06a4308_1
  - mkl=2023.1.0=h213fc3f_46344
  - mkl-service=2.4.0=py312h5eee18b_1
  - mkl_fft=1.3.8=py312h5eee18b_0
  - mkl_random=1.2.4=py312hdb19cb5_0
  - mpmath=1.3.0=py312h06a4308_0
  - ncurses=6.4=h6a678d5_0
  - nest-asyncio=1.6.0=pyhd8ed1ab_1
  - nettle=3.7.3=hbbd107a_1
  - networkx=3.2.1=py312h06a4308_0
  - ninja=1.12.1=h06a4308_0
  - ninja-base=1.12.1=hdb19cb5_0
  - nspr=4.35=h6a678d5_0
  - nss=3.89.1=h6a678d5_0
  - numpy=1.26.4=py312hc5e2394_0
  - numpy-base=1.26.4=py312h0da6c21_0
  - openh264=2.1.1=h4ff587b_0
  - openjpeg=2.4.0=h9ca470c_1
  - openssl=3.4.0=hb9d3cd8_0
  - packaging=23.2=py312h06a4308_0
  - pango=1.50.7=h05da053_0
  - parso=0.8.4=pyhd8ed1ab_1
  - pcre2=10.42=hebb0a14_1
  - pexpect=4.9.0=pyhd8ed1ab_1
  - pickleshare=0.7.5=pyhd8ed1ab_1004
  - pillow=10.3.0=py312h5eee18b_0
  - pip=24.3.1=pyh8b19718_0
  - pixman=0.40.0=h7f8727e_1
  - platformdirs=3.10.0=py312h06a4308_0
  - pluggy=1.0.0=py312h06a4308_1
  - poppler=22.12.0=h9614445_3
  - poppler-data=0.4.11=h06a4308_1
  - prompt-toolkit=3.0.48=pyha770c72_1
  - ptyprocess=0.7.0=pyhd8ed1ab_1
  - pure_eval=0.2.3=pyhd8ed1ab_1
  - pybind11-abi=5=hd3eb1b0_0
  - pycosat=0.6.6=py312h5eee18b_1
  - pycparser=2.21=pyhd3eb1b0_0
  - pygments=2.18.0=pyhd8ed1ab_1
  - pysocks=1.7.1=py312h06a4308_0
  - python=3.12.4=h5148396_1
  - python-dateutil=2.9.0.post0=pyhff2d567_1
  - python-graphviz=0.20.1=py312h06a4308_1
  - pytorch=2.3.1=py3.12_cuda12.1_cudnn8.9.2_0
  - pytorch-cuda=12.1=ha16c6d3_5
  - pytorch-mutex=1.0=cuda
  - pyyaml=6.0.1=py312h5eee18b_0
  - pyzmq=25.1.2=py312h6a678d5_0
  - readline=8.2=h5eee18b_0
  - reproc=14.2.4=h6a678d5_2
  - reproc-cpp=14.2.4=h6a678d5_2
  - requests=2.31.0=py312h06a4308_1
  - ruamel.yaml=0.17.21=py312h5eee18b_0
  - sqlite=3.45.3=h5eee18b_0
  - stack_data=0.6.3=pyhd8ed1ab_1
  - sympy=1.12=py312h06a4308_0
  - tbb=2021.8.0=hdb19cb5_0
  - tk=8.6.14=h39e8969_0
  - torchaudio=2.3.1=py312_cu121
  - torchvision=0.18.1=py312_cu121
  - tornado=6.4.2=py312h5eee18b_0
  - tqdm=4.66.2=py312he106c6f_0
  - traitlets=5.14.3=pyhd8ed1ab_1
  - truststore=0.8.0=py312h06a4308_0
  - urllib3=2.1.0=py312h06a4308_1
  - wcwidth=0.2.13=pyhd8ed1ab_1
  - wheel=0.43.0=py312h06a4308_0
  - xz=5.4.6=h5eee18b_1
  - yaml=0.2.5=h7b6447c_0
  - yaml-cpp=0.8.0=h6a678d5_1
  - zeromq=4.3.5=h6a678d5_0
  - zipp=3.21.0=pyhd8ed1ab_1
  - zlib=1.2.13=h4ab18f5_6
  - zstandard=0.22.0=py312h2c38b39_0
  - zstd=1.5.5=hc292b87_2
  - pip:
      - absl-py==2.1.0
      - aiofiles==23.2.1
      - albucore==0.0.24
      - albumentations==2.0.8
      - annotated-types==0.7.0
      - anyio==4.7.0
      - astroid==2.13.5
      - astunparse==1.6.3
      - beautifulsoup4==4.12.3
      - blessed==1.20.0
      - blinker==1.8.2
      - click==8.1.7
      - cmeel==0.53.3
      - cmeel-assimp==5.3.1
      - cmeel-boost==1.83.0
      - cmeel-console-bridge==*******
      - cmeel-octomap==*******
      - cmeel-qhull==*******
      - cmeel-tinyxml==*******
      - cmeel-urdfdom==*******
      - colorama==0.4.6
      - coloredlogs==15.0.1
      - contourpy==1.2.1
      - cycler==0.12.1
      - cython==3.1.2
      - cython-bbox==0.1.5
      - diffusers==0.32.2
      - dill==0.3.9
      - docker-pycreds==0.4.0
      - eigenpy==3.5.1
      - einops==0.8.1
      - fairscale==0.4.4
      - fake-factory==9999.9.9
      - fastapi==0.115.6
      - ffmpy==0.4.0
      - flask==3.0.3
      - flask-cors==4.0.1
      - flatbuffers==24.3.25
      - fonttools==4.53.1
      - fsspec==2024.6.1
      - gast==0.6.0
      - gdown==5.2.0
      - gitdb==4.0.11
      - gitpython==3.1.43
      - google-pasta==0.2.0
      - gpustat==1.1.1
      - gradio==5.9.1
      - gradio-client==1.5.2
      - graphviz==0.20.3
      - grpcio==1.64.1
      - h11==0.14.0
      - h5py==3.11.0
      - hpp-fcl==2.4.4
      - httpcore==1.0.7
      - httpx==0.28.1
      - huggingface-hub==0.29.2
      - humanfriendly==10.0
      - icecream==2.1.3
      - imageio==2.36.0
      - imutils==0.5.4
      - ipdb==0.13.13
      - isort==5.13.2
      - itsdangerous==2.2.0
      - joblib==1.4.2
      - keras==3.9.0
      - kiwisolver==1.4.5
      - lazy-object-proxy==1.10.0
      - libclang==18.1.1
      - lxml==5.3.0
      - markdown==3.6
      - markdown-it-py==3.0.0
      - matplotlib==3.10.0
      - mccabe==0.7.0
      - mdurl==0.1.2
      - ml-dtypes==0.5.1
      - namex==0.0.8
      - nvidia-cuda-runtime-cu12==12.6.37
      - nvidia-ml-py==12.555.43
      - onnx==1.17.0
      - onnxruntime==1.21.0
      - onnxruntime-extensions==0.14.0
      - onnxsim==0.4.36
      - opencv-python==*********
      - opencv-python-headless==*********
      - opt-einsum==3.3.0
      - optimum==1.24.0
      - optree==0.11.0
      - orjson==3.10.12
      - pandas==2.2.3
      - pin==2.7.0
      - polygraphy==0.49.26
      - prefetch-generator==1.0.3
      - prettytable==3.10.0
      - protobuf==4.25.3
      - psutil==6.0.0
      - ptflops==0.7.4
      - py-cpuinfo==9.0.0
      - pycocotools==2.0.8
      - pydantic==2.10.3
      - pydantic-core==2.27.1
      - pydub==0.25.1
      - pylint==2.15.7
      - pyparsing==3.1.2
      - python-multipart==0.0.20
      - pytz==2024.2
      - regex==2024.5.15
      - rich==13.7.1
      - ruff==0.8.3
      - safehttpx==0.1.6
      - safetensors==0.4.3
      - scikit-learn==1.6.1
      - scipy==1.14.0
      - seaborn==0.13.2
      - semantic-version==2.10.0
      - sentry-sdk==2.19.2
      - setproctitle==1.3.4
      - setuptools==75.1.0
      - shellingham==1.5.4
      - simplejson==3.19.3
      - simsimd==6.5.0
      - six==1.16.0
      - smmap==5.0.1
      - sniffio==1.3.1
      - soupsieve==2.6
      - starlette==0.41.3
      - stringzilla==3.12.5
      - tabulate==0.9.0
      - tensorboard==2.19.0
      - tensorboard-data-server==0.7.2
      - tensorboard-logger==0.1.0
      - tensorflow==2.19.0
      - tensorrt==10.3.0
      - tensorrt-cu12==10.3.0
      - tensorrt-cu12-bindings==10.3.0
      - tensorrt-cu12-libs==10.3.0
      - termcolor==2.4.0
      - tf-keras==2.19.0
      - thop==0.1.1-2209072238
      - threadpoolctl==3.5.0
      - timm==0.4.12
      - tokenizers==0.21.1
      - tomlkit==0.13.2
      - torch-pruning==1.5.1
      - torchsummary==1.5.1
      - transformers==4.49.0
      - transforms==0.2.1
      - typer==0.15.1
      - typing-extensions==4.12.2
      - tzdata==2024.2
      - ultralytics==8.3.133
      - ultralytics-thop==2.0.14
      - uvicorn==0.34.0
      - visdom==0.2.4
      - wandb==0.19.8
      - websocket-client==1.8.0
      - websockets==14.1
      - werkzeug==3.0.3
      - wrapt==1.16.0
      - yacs==0.1.8
prefix: /home/<USER>/miniconda3
