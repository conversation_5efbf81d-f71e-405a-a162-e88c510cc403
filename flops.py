import torch
from torchsummary import summary
from thop import profile
from thop import clever_format
import torchvision.models as models
from models import *
# 假设我们有一个预训练的模型
model = vgg13_nonid_pcm(num_classes= 10).cuda()
model.eval()

# 使用thop分析模型的运算量和参数量
input = torch.randn(1, 3, 32, 32).cuda()  # 随机生成一个输入张量，这个尺寸应该与模型输入的尺寸相匹配
flops, params = profile(model, inputs=(input,))

# 将结果转换为更易于阅读的格式
flops, params = clever_format([flops, params], '%.3f')

print(f"运算量：{flops}, 参数量：{params}")

