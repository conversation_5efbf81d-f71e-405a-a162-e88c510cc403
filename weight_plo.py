
import numpy as np
import matplotlib.pyplot as plt
 
# 设置正态分布参数
mu, sigma = 0, 1
# 创建一个包含1000个数值的序列，从正态分布中生成
Titanic = np.linspace(mu - 3*sigma, mu + 3*sigma, 1000)
# Pandas模块绘制直方图和核密度图
# 绘制直方图
Titanic.plot(kind = 'hist', bins = 20, color = 'steelblue', edgecolor = 'black', normed = True, label = '直方图')
# 绘制核密度图
Titanic.plot(kind = 'kde', color = 'red', label = '核密度图')
# 添加x轴和y轴标签
plt.xlabel('年龄')
plt.ylabel('核密度值')
# 添加标题
plt.title('乘客年龄分布')
# 显示图例
plt.legend()
# 显示图形
plt.show()
 
# 显示图形
plt.show()

plt.savefig("weight/pol.png")
 
# 显示图表
plt.show()