#!/bin/bash

# Simplified script to reproduce Figure 2(b) baseline experiment
# Tests both RRAM and PCM noise types with multiple noise levels
# Supports both CIFAR-10 and CIFAR-100

echo "=========================================="
echo "Figure 2(b) Baseline Experiment (Simplified)"
echo "=========================================="
echo ""

# Parse command line arguments
DATASET=${1:-cifar10}

if [ "$DATASET" = "cifar10" ]; then
    MODEL_PATH="checkpoint_NC/rram/cifar10/vgg16_nonid/ckpt_cifar10_nonid_0.0.pth"
elif [ "$DATASET" = "cifar100" ]; then
    MODEL_PATH="checkpoint_NC/cifar100/vgg16/ckpt_cifar100_nonid_0.0.pth"
else
    echo "Error: Unsupported dataset '$DATASET'. Use 'cifar10' or 'cifar100'."
    exit 1
fi

# Check if model exists
if [ ! -f "$MODEL_PATH" ]; then
    echo "Error: Model file not found at $MODEL_PATH"
    if [ "$DATASET" = "cifar100" ]; then
        echo "You need to train the CIFAR-100 model first using:"
        echo "python main_vgg.py --dataset cifar100 --model_name vgg16 --noise 0.0 --epoch 200 --lr 0.01 --batch_size 128"
    fi
    exit 1
fi

echo "Using nominal VGG16 model: $MODEL_PATH"
echo "Dataset: $DATASET"
echo ""

# Test RRAM noise levels
echo "Testing RRAM Noise (Log-normal Multiplicative)"
echo "Formula: W_new = W_nominal * exp(θ), θ ~ N(0, σ²)"
echo "----------------------------------------------"

for sigma in 0.0 0.1 0.2 0.3 0.4 0.5; do
    echo "Testing RRAM σ=$sigma"
    python quick_test_baseline.py --dataset $DATASET --model_path "$MODEL_PATH" --noise_type rram --noise_level $sigma
    echo ""
done

echo ""
echo "Testing PCM Noise (Perceptual Normal Additive)"
echo "Formula: W_new = W_nominal + N(0, σ²), σ = η * W_max"
echo "---------------------------------------------------"

for eta in 0.0 0.02 0.04 0.06 0.08 0.1 0.12; do
    echo "Testing PCM η=$eta"
    python quick_test_baseline.py --dataset $DATASET --model_path "$MODEL_PATH" --noise_type pcm --noise_level $eta
    echo ""
done

echo "=========================================="
echo "Figure 2(b) Baseline Experiment Completed"
echo "Dataset: $DATASET"
echo "=========================================="
echo ""
echo "Usage: $0 [cifar10|cifar100]"
echo "Example: $0 cifar100"
