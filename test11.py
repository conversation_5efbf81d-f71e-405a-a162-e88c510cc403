import torch
import torch.nn as nn
 
# 定义一个示例模型
class SimpleModel(nn.Module):
    def __init__(self):
        super(SimpleModel, self).__init__()
        self.conv1 = nn.Conv2d(1, 10, kernel_size=5)
        self.conv2 = nn.Conv2d(10, 20, kernel_size=5)
 
    def forward(self, x):
        x = self.conv1(x)
        x = self.conv2(x)
        return x

# 创建模型实例
model = SimpleModel()

# 用于存储中间特征图的列表
intermediate_outputs = []

# 定义前向钩子
def get_intermediate_layers(model, x, hooks):
    def hook(module, input, output):
        intermediate_outputs.append(output.detach())

    for name, layer in model.named_modules():
        if 'conv' in name:
            hooks.append(layer.register_forward_hook(hook))

# 使用列表来存储钩子
hooks = []
# 注册所有需要获取输出的层的钩子
get_intermediate_layers(model, torch.randn(1, 1, 28, 28), hooks)

# 运行模型以触发钩子
model(torch.randn(1, 1, 28, 28))

# 移除钩子
for hook in hooks:
    hook.remove()

# 打印所有中间特征图
for i, intermediate_output in enumerate(intermediate_outputs):
    print(f"特征图 {i+1}: {intermediate_output.shape}")