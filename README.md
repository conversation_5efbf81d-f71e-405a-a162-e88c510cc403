# Train CIFAR10 with <PERSON><PERSON><PERSON>or<PERSON>

I'm playing with [<PERSON>yTor<PERSON>](http://pytorch.org/) on the CIFAR10 dataset.

## Prerequisites
- Python 3.6+
- PyTorch 1.0+

## Training
```
# Start training with: 
python main.py

# You can manually resume the training with: 
python main.py --resume --lr=0.01
```

## Accuracy
| Model             | Acc.        |
| ----------------- | ----------- |
| [VGG16](https://arxiv.org/abs/1409.1556)              | 92.64%      |
| [ResNet18](https://arxiv.org/abs/1512.03385)          | 93.02%      |
| [ResNet50](https://arxiv.org/abs/1512.03385)          | 93.62%      |
| [ResNet101](https://arxiv.org/abs/1512.03385)         | 93.75%      |
| [RegNetX_200MF](https://arxiv.org/abs/2003.13678)     | 94.24%      |
| [RegNetY_400MF](https://arxiv.org/abs/2003.13678)     | 94.29%      |
| [MobileNetV2](https://arxiv.org/abs/1801.04381)       | 94.43%      |
| [ResNeXt29(32x4d)](https://arxiv.org/abs/1611.05431)  | 94.73%      |
| [ResNeXt29(2x64d)](https://arxiv.org/abs/1611.05431)  | 94.82%      |
| [SimpleDLA](https://arxiv.org/abs/1707.064)           | 94.89%      |
| [DenseNet121](https://arxiv.org/abs/1608.06993)       | 95.04%      |
| [PreActResNet18](https://arxiv.org/abs/1603.05027)    | 95.11%      |
| [DPN92](https://arxiv.org/abs/1707.01629)             | 95.16%      |
| [DLA](https://arxiv.org/pdf/1707.06484.pdf)           | 95.47%      |





train: noise:0
test: noise:0(93.73); noise:0.1(93.29); noise:0.2(92.41); noise:0.3(89.95); noise:0.4(83.15); noise:0.5(66.79);noise:0.6(36.26)

train: noise:0.1
test: noise:0(93.79); noise:0.1(93.51); noise:0.2(92.60); noise:0.3(91.13); noise:0.4(85.94); noise:0.5(74.24);noise:0.6(47.69)

train: noise:0.2
test: noise:0(93.62); noise:0.1(93.46); noise:0.2(93.02); noise:0.3(92.02); noise:0.4(89.53); noise:0.5(81.23);noise:0.6(59.85)

train: noise:0.3
test: noise:0(92.69); noise:0.1(92.68); noise:0.2(92.48); noise:0.3(92.10); noise:0.4(90.28); noise:0.5(84.71);noise:0.6(68.39)

train: noise:0.4
test: noise:0(90.62); noise:0.1(91.00); noise:0.2(91.48); noise:0.3(91.70); noise:0.4(91.30); noise:0.5(88.39);noise:0.6(78.74)

train: noise:0.5
test: noise:0(84.53); noise:0.1(85.10); noise:0.2(86.77); noise:0.3(88.70); noise:0.4(89.85); noise:0.5(89.61);noise:0.6(84.20)

train: noise:0.6
test: noise:0(60.63); noise:0.1(62.78); noise:0.2(69.74); noise:0.3(75.32); noise:0.4(81.59); noise:0.5(86.51);noise:0.6(87.15)

train: teacher:0.1;0.5; student:0.3 epoch:40
test: noise:0(91.60); noise:0.1(91.54); noise:0.2(91.22); noise:0.3(90.57); noise:0.4(88.79); noise:0.5(83.07);noise:0.6(69.41)

train: teacher:0.1;0.5; student:0.3 epoch:200
test: noise:0(92.93); noise:0.1(93.08); noise:0.2(92.98); noise:0.3(92.40); noise:0.4(91.32); noise:0.5(83.07);noise:0.6(69.41)

bian test: noise:0(93.53); noise:0.1(93.28); noise:0.2(92.98); noise:0.3(92.40); noise:0.4(91.42); noise:0.5(88.91)





--------------------------------------------------VGG8-------------------------------------------------------------------------
train: noise:0
test: noise:0(91.64); noise:0.1(90.58); noise:0.2(86.50); noise:0.3(78.85); noise:0.4(65.24); noise:0.5(45.38);noise:0.6(27.51)

train: noise:0.1
test: noise:0(91.22); noise:0.1(90.80); noise:0.2(88.59); noise:0.3(83.53); noise:0.4(74.76); noise:0.5(58.31);noise:0.6(36.96)

train: noise:0.2
test: noise:0(90.87); noise:0.1(90.28); noise:0.2(89.43); noise:0.3(86.51); noise:0.4(80.93); noise:0.5(67.96);noise:0.6(47.95)

train: noise:0.3
test: noise:0(89.72); noise:0.1(89.57); noise:0.2(88.99); noise:0.3(87.37); noise:0.4(83.67); noise:0.5(75.39);noise:0.6(56.02)

train: noise:0.4
test: noise:0(88.14); noise:0.1(88.22); noise:0.2(87.62); noise:0.3(86.78); noise:0.4(84.27); noise:0.5(79.45);noise:0.6(66.10)

train: noise:0.5
test: noise:0(84.08); noise:0.1(84.24); noise:0.2(85.18); noise:0.3(85.31); noise:0.4(83.91); noise:0.5(82.25);noise:0.6(74.35)

train: noise:0.6
test: noise:0(74.53); noise:0.1(75.43); noise:0.2(76.69); noise:0.3(79.41); noise:0.4(80.76); noise:0.5(80.90);noise:0.6(77.28)

test: noise:0(90.07); noise:0.1(90.00); noise:0.2(89.61); noise:0.3(88.78); noise:0.4(85.87); noise:0.5(79.99);noise:0.6()

bian test: noise:0(91.17); noise:0.1(90.20); noise:0.2(89.81); noise:0.3(88.78); noise:0.4(85.87); noise:0.5(79.99);noise:0.6()

0.1-0.5混合噪声训练
test: noise:0(90.38); noise:0.1(90.06); noise:0.2(88.83); noise:0.3(86.82); noise:0.4(82.29); noise:0.5(72.01);noise:0.6()
