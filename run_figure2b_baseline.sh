#!/bin/bash

# Script to reproduce Figure 2(b) baseline experiment
# This script tests a standard VGG16 model under different noise conditions

echo "=========================================="
echo "Figure 2(b) Baseline Experiment Runner"
echo "=========================================="
echo ""

# Set the path to the nominal (standard trained) VGG16 model
MODEL_PATH="checkpoint_NC/rram/cifar10/vgg16_nonid/ckpt_cifar10_nonid_0.0.pth"

# Check if model file exists
if [ ! -f "$MODEL_PATH" ]; then
    echo "Error: Model file not found at $MODEL_PATH"
    echo "Please ensure the model file exists before running this script."
    exit 1
fi

echo "Using nominal VGG16 model: $MODEL_PATH"
echo ""

# Test RRAM noise (log-normal multiplicative)
echo "=========================================="
echo "Testing RRAM Noise (Log-normal Multiplicative)"
echo "Formula: W_new = W_nominal * exp(θ), θ ~ N(0, σ²)"
echo "=========================================="
echo ""

python baseline_figure2b_test.py \
    --noise_type rram \
    --model_path "$MODEL_PATH" \
    --model_name vgg16 \
    --batch_size 64 \
    --num_trials 5 \
    --device auto

echo ""
echo "RRAM noise testing completed."
echo ""

# Test PCM noise (perceptual normal additive)
echo "=========================================="
echo "Testing PCM Noise (Perceptual Normal Additive)"
echo "Formula: W_new = W_nominal + N(0, σ²), σ = η * W_max"
echo "=========================================="
echo ""

python baseline_figure2b_test.py \
    --noise_type pcm \
    --model_path "$MODEL_PATH" \
    --model_name vgg16 \
    --batch_size 64 \
    --num_trials 5 \
    --device auto

echo ""
echo "PCM noise testing completed."
echo ""

echo "=========================================="
echo "Figure 2(b) Baseline Experiment Completed"
echo "=========================================="
echo ""
echo "The results above show how a standard VGG16 model"
echo "(trained without noise) performs under different"
echo "hardware noise conditions, which corresponds to"
echo "the baseline curves in Figure 2(b) of the paper."
