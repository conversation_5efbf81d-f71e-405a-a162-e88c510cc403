import torch
import torch.nn as nn

class MyModel(nn.Module):
    def __init__(self):
        super(MyModel, self).__init__()
        self.fc1 = nn.Linear(10, 20)
        self.fc2 = nn.Linear(20, 10)
        
    def forward(self, x):
        x = self.fc1(x)
        x = self.fc2(x)
        return x

model = MyModel()

import torch.optim as optim

# 准备训练数据和标签
data = torch.randn(100, 10)  # 100个样本，每个样本10维
labels = torch.randint(0, 2, (100,))  # 100个样本的标签，0或1

# 定义损失函数和优化器
criterion = nn.CrossEntropyLoss()
optimizer = optim.SGD(model.parameters(), lr=0.1)

# 训练模型
for epoch in range(100):
    optimizer.zero_grad()
    outputs = model(data)
    loss = criterion(outputs, labels)
    loss.backward()
    optimizer.step()

import matplotlib.pyplot as plt

# 可视化权重分布
weights = model.state_dict()
for key in weights:
        plt.hist(weights[key].numpy().flatten(), bins=50, alpha=0.5, label=key)
plt.legend()
plt.xlabel('Weight Value')
plt.ylabel('Frequency')
plt.title('Weight Distribution')
plt.savefig('figure.png')
plt.show()