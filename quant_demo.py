import torch
import torchvision
import torch.nn as nn
import torch.optim as optim
import torch.quantization as quantization
import torch_quantization as tq
from torchvision import transforms

# 定义一个简单的全连接神经网络作为示例模型
class SimpleModel(nn.Module):
    def __init__(self):
        super(SimpleModel, self).__init__()
        self.fc1 = nn.Linear(784, 256)
        self.relu = nn.ReLU()
        self.fc2 = nn.Linear(256, 10)
        self.softmax = nn.LogSoftmax(dim=1)

    def forward(self, x):
        x = x.view(-1, 784)  # 将输入展平为向量
        x = self.fc1(x)
        x = self.relu(x)
        x = self.fc2(x)
        x = self.softmax(x)
        return x

# 创建模型实例
model = SimpleModel()

# 定义量化配置 QConfig
# 自定义 QConfig，将权重量化为6位
custom_qconfig = tq.QuantQConfig(
    weight=tq.fixed_point_quantization(num_bits=6),
    activation=tq.pass_through_quantization()  # 激活保持原始精度
)

# 应用 QConfig 到模型的每一层
quantized_model = tq.quantize_qconfig(model, custom_qconfig)

# 打印量化后的模型结构
print(quantized_model)

# 在此处可以进行训练或者推理过程

# 可选：将模型转换为量化格式
quantized_model = tq.convert(quantized_model)

# 打印转换后的量化模型结构
print(quantized_model)
