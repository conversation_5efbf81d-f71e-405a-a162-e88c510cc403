'''Train CIFAR10 with PyTorch.'''
import torch
import torch.optim as optim
import torch.backends.cudnn as cudnn
from torch.utils.data import DataLoader
import torchvision
import torchvision.transforms as transforms
from torchvision import datasets, transforms

import os
import argparse

from models import *
from utils import progress_bar
from data import MyData, TinyImageNet


parser = argparse.ArgumentParser(description='PyTorch CIFAR10 Training')
parser.add_argument('--lr', default=0.01, type=float, help='learning rate')
parser.add_argument('--noise', default=0.0, type=float, help='noise')
parser.add_argument('--epoch', default=100, type=int, help='learning epoch')
parser.add_argument('--resume', '-r', action='store_true', help='resume from checkpoint')
parser.add_argument('--precheckpoint_root', help='Load precheckpoint', type=str)
parser.add_argument("--optim", default="SGD", help='optim', type=str)
parser.add_argument('--model_name', help='choice a model to train and eval. eg: alenet, vgg16', type=str)
parser.add_argument('--data_root', help='Path to the train dataset', type=str)
parser.add_argument('--batch_size', default=64, help='number of batch size', type=int)
parser.add_argument('--num_workers', default=8, help='number of workers', type=int)
parser.add_argument('--dataset', type=str, default='cifar10',help='training dataset (default: cifar100)')

args = parser.parse_args()
args.cuda = torch.cuda.is_available()
device = 'cuda' if torch.cuda.is_available() else 'cpu'
best_acc = 0  # best test accuracy
start_epoch = 0  # start from epoch 0 or last checkpoint epoch
num_c=0
# Data
print('==> Preparing data..')

kwargs = {'num_workers': 1, 'pin_memory': True} if args.cuda else {}
if args.dataset == 'cifar10':
    num_c=10
    train_loader = torch.utils.data.DataLoader(
        datasets.CIFAR10('./data.cifar10', train=True, download=True,
                       transform=transforms.Compose([
                           transforms.Pad(4),
                           transforms.RandomCrop(32),
                           transforms.RandomHorizontalFlip(),
                           transforms.ToTensor(),
                           transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2023, 0.1994, 0.2010))
                       ])),
        batch_size=args.batch_size, shuffle=True, **kwargs)
    test_loader = torch.utils.data.DataLoader(
        datasets.CIFAR10('./data.cifar10', train=False, transform=transforms.Compose([
                           transforms.ToTensor(),
                           transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2023, 0.1994, 0.2010))
                       ])),
        batch_size=args.batch_size, shuffle=True, **kwargs)
    pruning_test_loader = torch.utils.data.DataLoader(
        datasets.CIFAR10('./data.cifar10', train=False, transform=transforms.Compose([
                           transforms.ToTensor(),
                           transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2023, 0.1994, 0.2010))
                       ])),
        batch_size=64, shuffle=False, **kwargs)
elif args.dataset == 'cifar100':
    num_c=100
    train_loader = torch.utils.data.DataLoader(
        datasets.CIFAR100('./data.cifar100', train=True, download=True,
                       transform=transforms.Compose([
                           transforms.Pad(4),
                           transforms.RandomCrop(32),
                           transforms.RandomHorizontalFlip(),
                           transforms.ToTensor(),
                           transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2023, 0.1994, 0.2010))
                       ])),
        batch_size=args.batch_size, shuffle=True, **kwargs)
    test_loader = torch.utils.data.DataLoader(
        datasets.CIFAR100('./data.cifar100', train=False, transform=transforms.Compose([
                           transforms.ToTensor(),
                           transforms.Normalize((0.4914, 0.4822, 0.4465), (0.2023, 0.1994, 0.2010))
                       ])),
        batch_size=args.batch_size, shuffle=True, **kwargs)
elif args.dataset == 'tiny-imagenet':
    num_c=200
    train_dataset = TinyImageNet('./tiny-imagenet', split='train', download=True)
    test_dataset = TinyImageNet('./tiny-imagenet', split='val', download=True)

    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True, num_workers=args.num_workers)

    test_loader = DataLoader(test_dataset, batch_size=args.batch_size, shuffle=False, num_workers=args.num_workers)


print('train dataset len: {}'.format(len(train_loader.dataset)))

print('val dataset len: {}'.format(len(test_loader.dataset)))

# Model
# Model
print('==> Building model..')
if args.model_name == "vgg16": 
    net = vgg16(num_classes= num_c)
elif args.model_name == "vgg16_nonid": #ok
    net = vgg16_nonid(num_classes= num_c)
elif args.model_name == "vgg8": #ok
    net = vgg8(num_classes= num_c)
elif args.model_name == "vgg8_nonid": #ok
    net = vgg8_nonid(num_classes= num_c)
elif args.model_name == "squeezenet": #ok
    net = SqueezeNet(num_classes= num_c)
elif args.model_name == "squeezenet_nonid": #ok
    net = SqueezeNet_nonid(num_classes= num_c)
elif args.model_name == "ResNet18": #ok
    net = ResNet18()
elif args.model_name == "ResNet34":  #ok
    net = ResNet34()
elif args.model_name == "ResNet50": #ok
    net = ResNet50()
elif args.model_name == "PreActResNet18": #ok
    net = PreActResNet18()
elif args.model_name == "PreActResNet34": #ok
    net = PreActResNet34()
elif args.model_name == "PreActResNet50": #ok
    net = PreActResNet50()
elif args.model_name == "PreActResNet101": #ok
    net = PreActResNet101()
elif args.model_name == "PreActResNet152": #ok
    net = PreActResNet152()
elif args.model_name == "GoogLeNet": #ok
    net = GoogLeNet()
elif args.model_name == "DenseNet121": #ok
    net = DenseNet121()
elif args.model_name == "ResNeXt29_2x64d": #ok
    net = ResNeXt29_2x64d()
elif args.model_name == "MobileNet": #ok
    net = MobileNet()
elif args.model_name == "MobileNetV2": #ok
    net = MobileNetV2()
elif args.model_name == "DPN92": #ok
    net = DPN92()
elif args.model_name == "ShuffleNetG2":
    net = ShuffleNetG2()
elif args.model_name == "SENet18":  #ok
    net = SENet18()
elif args.model_name == "ShuffleNetV2":  #ok
    net = ShuffleNetV2(1)
elif args.model_name == "EfficientNetB0":  #ok
    net = EfficientNetB0()
elif args.model_name == "RegNetX_200MF":  #ok
    net = RegNetX_200MF()
elif args.model_name == "SimpleDLA":  #ok
    net = SimpleDLA()
elif args.model_name == "LeNet":  #ok
    net = LeNet()
elif args.model_name == "AlexNet":  #ok
    net = AlexNet(num_classes= num_c)
elif args.model_name == "AlexNet_nonid":  #ok
    net = AlexNet_nonid(num_classes= num_c)
elif args.model_name=="vit_small":
    net = ViT( image_size = 32, patch_size = 4, num_classes = 10, dim = int(512), depth = 6, heads = 8, mlp_dim = 512, dropout = 0.1, emb_dropout = 0.1)
else:
    print("Error: model name not define! Exit...")
    exit(1)

net = net.to(device)
# if device == 'cuda':
#     net = torch.nn.DataParallel(net)
#     cudnn.benchmark = True

if args.resume:
    # Load checkpoint.
    print('==> Resuming from checkpoint..')
    assert os.path.isdir('checkpoint'), 'Error: no checkpoint directory found!'
    checkpoint = torch.load(args.precheckpoint_root)
    net.load_state_dict(checkpoint['net'])
    best_acc = checkpoint['acc']
    start_epoch = checkpoint['epoch']

criterion = nn.CrossEntropyLoss()


optimizer = optim.SGD(net.parameters(), lr=args.lr, momentum=0.9, weight_decay=5e-4)
scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=200)


# Training
def train(epoch, sgm):
    print('\nEpoch: %d' % epoch)
    net.train()
    train_loss = 0
    correct = 0
    total = 0
    for batch_idx, (inputs, targets) in enumerate(train_loader):
        # print(inputs.shape)
        inputs, targets = inputs.to(device), targets.to(device)
        optimizer.zero_grad()
        outputs, _ = net(inputs, lamda = sgm)
        loss = criterion(outputs, targets)
        loss.backward()
        optimizer.step()

        train_loss += loss.item()
        _, predicted = outputs.max(1)
        total += targets.size(0)
        correct += predicted.eq(targets).sum().item()

        progress_bar(batch_idx, len(train_loader), 'Loss: %.3f | Acc: %.3f%% (%d/%d)'
                     % (train_loss / (batch_idx + 1), 100. * correct / total, correct, total))


def test(epoch, sgm):
    global best_acc
    net.eval()
    test_loss = 0
    correct = 0
    total = 0
    # for m in net.modules():
    #     if isinstance(m, nn.Conv2d):
    #         print(m.weight.data)

    with torch.no_grad():
        for batch_idx, (inputs, targets) in enumerate(test_loader):
            inputs, targets = inputs.to(device), targets.to(device)
            outputs, _ = net(inputs, lamda = sgm)
            loss = criterion(outputs, targets)

            test_loss += loss.item()
            _, predicted = outputs.max(1)
            total += targets.size(0)
            correct += predicted.eq(targets).sum().item()

            progress_bar(batch_idx, len(test_loader), 'Loss: %.3f | Acc: %.3f%% (%d/%d)'
                         % (test_loss / (batch_idx + 1), 100. * correct / total, correct, total))

    # Save checkpoint.
    acc = 100. * correct / total
    if acc > best_acc:
        print('Saving..')
        state = {
            'net': net.state_dict(),
            'acc': acc,
            'epoch': epoch,
        }
        if not os.path.isdir('checkpoint/'+args.dataset+'/'+args.model_name):
            os.mkdir('checkpoint/'+args.dataset+'/'+args.model_name)
        torch.save(net.state_dict(), 'checkpoint/'+args.dataset+'/'+args.model_name+'/ckpt_'+args.dataset+'_nonid_'+str(args.noise)+'_prune_l2.pth')
        best_acc = acc

def utils_pruning__test(noise_level):
    net.eval()
    for batch_idx, (inputs, targets) in enumerate(pruning_test_loader):
        inputs, targets = inputs.to(device), targets.to(device)
        _, feature_map = net(inputs, noise_level)
        break
    return feature_map

def feature_mse(feature_map1, feature_map2):
    feature_mse = []
    mse_loss = torch.nn.MSELoss()
    for i_f in range(len(feature_map1)):
        vector_sme = []
        for i in range(feature_map1[i_f].shape[1]):
            if len(feature_map1[i_f].shape)>2:
                A = feature_map1[i_f][:,i,:,:].cpu().detach().numpy()
                B = feature_map2[i_f][:,i,:,:].cpu().detach().numpy()
                mse = np.mean((A - B) ** 2)
                f_mean = (np.mean(np.abs(A))+np.mean(np.abs(B)))/2
                vector_sme.append(mse/f_mean)
            else:
                A = feature_map1[i_f][:,i].cpu().detach().numpy()
                B = feature_map2[i_f][:,i].cpu().detach().numpy()
                mse = np.mean((A - B) ** 2)
                f_mean = (np.mean(np.abs(A))+np.mean(np.abs(B)))/2
                vector_sme.append(mse/f_mean)
        feature_mse.append(vector_sme)
    return feature_mse

def list_sum(list1, list2, list4, list5, list_sum):
    for i in range(len(list1)):
        for j in range(len(list1[i])):
            list_sum[i][j]=list1[i][j]+list2[i][j]+list4[i][j]+list5[i][j]
    return list_sum


def all_feature_mse():
    #feature_map0 = utils_pruning__test(0.0)
    feature_map1 = utils_pruning__test(0.1)
    feature_map2 = utils_pruning__test(0.2)
    feature_map3 = utils_pruning__test(0.3)
    feature_map4 = utils_pruning__test(0.4)
    feature_map5 = utils_pruning__test(0.5)

    feature_mse1 = feature_mse(feature_map3, feature_map1)
    feature_mse2 = feature_mse(feature_map3, feature_map2)
    feature_mse3 = feature_mse(feature_map3, feature_map3)
    feature_mse4 = feature_mse(feature_map3, feature_map4)
    feature_mse5 = feature_mse(feature_map3, feature_map5)
    feature_mse_sum = feature_mse(feature_map3, feature_map3)

    feature_mse_sum = list_sum(feature_mse1, feature_mse2, feature_mse4, feature_mse5, feature_mse_sum)
    return feature_mse_sum

def pruning_mask():
    feature_mse_sum = all_feature_mse()
    PR = 0.75
    cfg = [int(PR*64), int(PR*64), int(PR*128), int(PR*128), int(PR*256), int(PR*256), int(PR*256), int(PR*512), int(PR*512), int(PR*512), 
            int(PR*512), int(PR*512), int(PR*512), 256, 128]
    #print(cfg)
    cfg_mask = []
    layer_id = 0
    for m in net.modules():
        if isinstance(m, nn.Conv2d):
            out_channels = m.weight.data.shape[0]
            if out_channels == cfg[layer_id]:
                cfg_mask.append(torch.ones(out_channels))
                layer_id += 1
                continue
            weight_copy = m.weight.data.abs().clone()
            weight_copy = weight_copy.cpu().numpy()
            L1_norm = np.array(feature_mse_sum[layer_id])
            arg_max = np.argsort(L1_norm)
            arg_max_rev = arg_max[:cfg[layer_id]] 
            assert arg_max_rev.size == cfg[layer_id], "size of arg_max_rev not correct"
            mask = torch.zeros(out_channels)
            mask[arg_max_rev.tolist()] = 1
            cfg_mask.append(mask)
            layer_id += 1
        elif isinstance(m, nn.Linear):
            layer_id += 1
    return cfg_mask

def pruning_mask_PFEC():
    PR = 0.75
    cfg = [int(PR*64), int(PR*64), int(PR*128), int(PR*128), int(PR*256), int(PR*256), int(PR*256), int(PR*512), int(PR*512), int(PR*512), 
            int(PR*512), int(PR*512), int(PR*512), 256, 128]
    print(cfg)
    cfg_mask = []
    layer_id = 0
    for m in net.modules():
        if isinstance(m, nn.Conv2d):
            out_channels = m.weight.data.shape[0]
            if out_channels == cfg[layer_id]:
                cfg_mask.append(torch.ones(out_channels))
                layer_id += 1
                continue
            weight_copy = m.weight.data.abs().clone()
            weight_copy = weight_copy.cpu().numpy()
            L1_norm = np.sum(weight_copy, axis=(1, 2, 3))
            arg_max = np.argsort(L1_norm)
            arg_max_rev = arg_max[::-1][:cfg[layer_id]]
            assert arg_max_rev.size == cfg[layer_id], "size of arg_max_rev not correct"
            mask = torch.zeros(out_channels)
            mask[arg_max_rev.tolist()] = 1
            cfg_mask.append(mask)
            layer_id += 1
        elif isinstance(m, nn.Linear):
            layer_id += 1
    return cfg_mask



def make_filter_zero():
    cfg_mask = pruning_mask_PFEC()
    layer_id_in_cfg = 0
    for m in net.modules():
        if isinstance(m, nn.Conv2d):
            w = m.weight.data.clone()
            for idex in range(m.weight.data.shape[0]):
                w[idex,:,:,:] = w[idex,:,:,:] * cfg_mask[layer_id_in_cfg][idex]
            m.weight.data = w.clone()
            layer_id_in_cfg += 1
            #w1 = m.weight.data[:, idx0.tolist(), :, :].clone()



class Mask:
    def __init__(self, model):
        self.model_size = {}
        self.model_length = {}
        self.compress_rate = {}
        self.distance_rate = {}
        self.mat = {}
        self.model = model
        self.mask_index = []
        self.filter_small_index = {}
        self.filter_large_index = {}
        self.similar_matrix = {}
        self.norm_matrix = {}
        self.cfg = [32, 64, 'M', 128, 128, 'M', 256, 256, 256, 'M', 256, 256, 256, 'M', 256, 256, 256]

    def get_codebook(self, weight_torch, compress_rate, length):
        weight_vec = weight_torch.view(length)
        weight_np = weight_vec.cpu().numpy()

        weight_abs = np.abs(weight_np)
        weight_sort = np.sort(weight_abs)

        threshold = weight_sort[int(length * (1 - compress_rate))]
        weight_np[weight_np <= -threshold] = 1
        weight_np[weight_np >= threshold] = 1
        weight_np[weight_np != 1] = 0

        print("codebook done")
        return weight_np

    def get_filter_codebook(self, weight_torch, compress_rate, length):
        codebook = np.ones(length)
        if len(weight_torch.size()) == 4:
            filter_pruned_num = int(weight_torch.size()[0] * (1 - compress_rate))
            weight_vec = weight_torch.view(weight_torch.size()[0], -1) #每个过滤器展开
            #print(weight_vec.shape)
            norm2 = torch.norm(weight_vec, 2, 1) # 计算每个过滤器的l2norm
            #print(norm2.shape)
            norm2_np = norm2.cpu().numpy()
            filter_index = norm2_np.argsort()[:filter_pruned_num] #从小到大排序，取需要剪枝的通道索引
            #            norm1_sort = np.sort(norm1_np)
            #            threshold = norm1_sort[int (weight_torch.size()[0] * (1-compress_rate) )]
            kernel_length = weight_torch.size()[1] * weight_torch.size()[2] * weight_torch.size()[3] #获取每个过滤器的长度
            for x in range(0, len(filter_index)):
                codebook[filter_index[x] * kernel_length: (filter_index[x] + 1) * kernel_length] = 0  #将l2-norm小的过滤器权重全部置零

            print("filter codebook done")
        else:
            pass
        return codebook
    
    def get_filter_codebook_my(self, weight_torch, compress_rate, length, feature_mse):
        codebook = np.ones(length)
        if len(weight_torch.size()) == 4:
            filter_pruned_num = int(weight_torch.size()[0] * (1 - compress_rate))
            #weight_vec = weight_torch.view(weight_torch.size()[0], -1) #每个过滤器展开
            #print(weight_vec.shape)
            #norm2 = torch.norm(weight_vec, 2, 1) # 计算每个过滤器的l2norm
            norm2_np = feature_mse

            #print(norm2_np.shape)
            #norm2_np = norm2.cpu()
            filter_index = norm2_np.argsort()[::-1][:filter_pruned_num] #从大到小排序，取需要剪枝的通道索引
            #            norm1_sort = np.sort(norm1_np)
            #            threshold = norm1_sort[int (weight_torch.size()[0] * (1-compress_rate) )]
            kernel_length = weight_torch.size()[1] * weight_torch.size()[2] * weight_torch.size()[3] #获取每个过滤器的长度
            for x in range(0, len(filter_index)):
                codebook[filter_index[x] * kernel_length: (filter_index[x] + 1) * kernel_length] = 0  #将l2-norm小的过滤器权重全部置零

            print("filter codebook done")
        else:
            pass
        return codebook

    def get_filter_index(self, weight_torch, compress_rate, length):
        if len(weight_torch.size()) == 4:
            filter_pruned_num = int(weight_torch.size()[0] * (1 - compress_rate))
            weight_vec = weight_torch.view(weight_torch.size()[0], -1)
            # norm1 = torch.norm(weight_vec, 1, 1)
            # norm1_np = norm1.cpu().numpy()
            norm2 = torch.norm(weight_vec, 2, 1)
            norm2_np = norm2.cpu().numpy()
            filter_small_index = []
            filter_large_index = []
            filter_large_index = norm2_np.argsort()[filter_pruned_num:]
            filter_small_index = norm2_np.argsort()[:filter_pruned_num]
            #            norm1_sort = np.sort(norm1_np)
            #            threshold = norm1_sort[int (weight_torch.size()[0] * (1-compress_rate) )]
            kernel_length = weight_torch.size()[1] * weight_torch.size()[2] * weight_torch.size()[3]
            # print("filter index done")
        else:
            pass
        return filter_small_index, filter_large_index

    def convert2tensor(self, x):
        x = torch.FloatTensor(x)
        return x

    def init_length(self):
        for index, item in enumerate(self.model.parameters()):
            self.model_size[index] = item.size()

        for index1 in self.model_size:
            for index2 in range(0, len(self.model_size[index1])):
                if index2 == 0:
                    self.model_length[index1] = self.model_size[index1][0]
                else:
                    self.model_length[index1] *= self.model_size[index1][index2]
        print(self.model_length)

    def init_rate(self, rate_norm_per_layer, rate_dist_per_layer, pre_cfg=True):
        cfg = [32, 64, 128, 128, 256, 256, 256, 256, 256, 256, 256, 256, 256]
        cfg_index = 0
        for index, item in enumerate(self.model.named_parameters()):
            #print(item[1].size())
            self.compress_rate[index] = 1
            self.distance_rate[index] = 1
            if len(item[1].size()) == 4:#确定这一层是卷积层
                #print(item[1].size())
                if not pre_cfg:
                    self.compress_rate[index] = rate_norm_per_layer #确定了每层的剪枝率
                    self.distance_rate[index] = rate_dist_per_layer
                    self.mask_index.append(index) #确定了剪枝哪些层
                    
                    #print(item[0], "self.mask_index", self.mask_index)
                else:
                    self.compress_rate[index] = rate_norm_per_layer
                    self.distance_rate[index] = 1 - cfg[cfg_index] / item[1].size()[0]
                    self.mask_index.append(index)
                    #print(item[0], "self.mask_index", self.mask_index, cfg_index, cfg[cfg_index], item[1].size()[0],self.distance_rate[index], )
                    #print("self.distance_rate", self.distance_rate)
                    cfg_index += 1
        
    def init_mask(self, rate_norm_per_layer, rate_dist_per_layer, dist_type):
        self.init_rate(rate_norm_per_layer, rate_dist_per_layer, pre_cfg=False)
        layer_id = 0
        for index, item in enumerate(self.model.parameters()):
            if index in self.mask_index: #找到要剪枝的层
                # mask for norm criterion
                self.mat[index] = self.get_filter_codebook(item.data, self.compress_rate[index],self.model_length[index]) #将filter l2参数较小的全部置零
                # feature_mse_sum = all_feature_mse()
                # feature_mse = np.array(feature_mse_sum[layer_id])
                # self.mat[index] = self.get_filter_codebook_my(item.data, self.compress_rate[index],self.model_length[index], feature_mse)
                
                self.mat[index] = self.convert2tensor(self.mat[index])
                #print(self.mat[index])
                if args.cuda:
                    self.mat[index] = self.mat[index].cuda()
                layer_id +=1

        print("mask Ready")

    def do_mask(self):
        for index, item in enumerate(self.model.parameters()):
            if index in self.mask_index:
                a = item.data.view(self.model_length[index])
                b = a * self.mat[index]
                item.data = b.view(self.model_size[index])
        print("mask Done")

    def do_similar_mask(self):
        for index, item in enumerate(self.model.parameters()):
            if index in self.mask_index:
                a = item.data.view(self.model_length[index])
                b = a * self.similar_matrix[index]
                item.data = b.view(self.model_size[index])
        print("mask similar Done")

    def do_grad_mask(self):
        for index, item in enumerate(self.model.parameters()):
            if index in self.mask_index:
                a = item.grad.data.view(self.model_length[index])
                # reverse the mask of model
                # b = a * (1 - self.mat[index])
                b = a * self.mat[index]
                b = b * self.similar_matrix[index]
                item.grad.data = b.view(self.model_size[index])
        # print("grad zero Done")

    def if_zero(self):
        for index, item in enumerate(self.model.parameters()):
            if (index in self.mask_index):
                # if index == 0:
                a = item.data.view(self.model_length[index])
                b = a.cpu().numpy()
                print(
                    "number of nonzero weight is %d, zero is %d" % (np.count_nonzero(b), len(b) - np.count_nonzero(b)))


m = Mask(net)
m.init_length()
m.model = net
m.init_mask(0.5, 0.1, 'l2')
m.do_mask()
net = m.model
#test(0, args.noise)
for epoch in range(start_epoch, start_epoch + args.epoch):
    train(epoch, args.noise)
    m.if_zero()
    m.init_mask(0.5, 0.1, 'l2')
    m.do_mask()
    m.if_zero()
    net = m.model
    test(epoch, args.noise)
    scheduler.step()
