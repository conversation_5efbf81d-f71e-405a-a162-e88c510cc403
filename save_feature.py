import torch
import torchvision.transforms as transforms
import matplotlib.pyplot as plt
import numpy as np
from torchvision import models
import torch.nn as nn
import torch


def add_noise_to_conv_layers(model, std=0.1):
    for module in model.children():
        if isinstance(module, nn.Conv2d):
            # 添加高斯噪声到卷积层的权重和偏置
            with torch.no_grad():
                weight_copy = module.weight.data.clone()
                noise = torch.normal(0, std, size=weight_copy.size())             
                module.weight.data= torch.exp(noise) * weight_copy
                #print(module)
        elif isinstance(module, nn.Linear):
            # 添加高斯噪声到卷积层的权重和偏置
            with torch.no_grad():
                weight_copy = module.weight.data.clone() 
                noise = torch.normal(0, std, size=weight_copy.size())             
                module.weight.data= torch.exp(noise) * weight_copy
                #print(module)
        elif isinstance(module, nn.Module):
            # 如果是子模块，则递归调用函数
            add_noise_to_conv_layers(module, std=std)
            #print(module)

def add_noise_to_conv_layers_pcm(model, yita=0.1):
    for module in model.children():
        if isinstance(module, nn.Conv2d):
            # 添加高斯噪声到卷积层的权重和偏置
            with torch.no_grad():
                weight_copy = module.weight.data.clone() 
                max_value = torch.max(weight_copy)
                std = yita * max_value
                noise = torch.normal(0, std, size=weight_copy.size())             
                module.weight.data= noise + weight_copy
                #print(module)
        elif isinstance(module, nn.Linear):
            # 添加高斯噪声到卷积层的权重和偏置
            with torch.no_grad():
                weight_copy = module.weight.data.clone() 
                max_value = torch.max(weight_copy)
                std = yita * max_value
                noise = torch.normal(0, std, size=weight_copy.size())             
                module.weight.data= noise + weight_copy
                #print(module)
        elif isinstance(module, nn.Module):
            # 如果是子模块，则递归调用函数
            add_noise_to_conv_layers_pcm(module, yita=yita)
            #print(module)



# 假设我们使用的是 ResNet18 作为示例网络
model = models.resnet18(pretrained=True)
#add_noise_to_conv_layers(model, std= 0.5*1.5)
#add_noise_to_conv_layers_pcm(model, yita= 0.06)
model.eval()



# 选择我们感兴趣的层，例如第二个卷积层的输出
def get_interested_layer_output(x):
    # 定义一个钩子函数来获取感兴趣层的输出
    def hook_fn(module, input, output):
        hook_output.append(output)
    hook_output = []
    # 假设你想要抓取第二个卷积层的输出
    print(model)
    target_layer = model.layer3[0].conv1
    handle = target_layer.register_forward_hook(hook_fn)
    return hook_output, handle

# 准备输入数据
input_image = torch.randn(1, 3, 224, 224)  # 随机生成一个图像
hook_output, handle = get_interested_layer_output(input_image)

# 前向传播以获取特征图
with torch.no_grad():
    _ = model(input_image)

# 解除钩子
handle.remove()

# 获取特征图
print(len(hook_output))
feature_map = hook_output[0].squeeze().cpu().numpy()

# 将特征图进行归一化处理
feature_map -= feature_map.min()
feature_map /= feature_map.max()

# 假设我们只取第一个特征图来可视化
feature_map_to_plot = feature_map[0]

# 使用 Matplotlib 生成热图
plt.imshow(feature_map_to_plot, cmap='viridis')  # 可以选择其他 colormap
plt.colorbar()
plt.title('Feature Map Heatmap')
plt.savefig('feature/feature_map_heatmap.png')
plt.show()
